name: dostafka
version: 1.5.0+2
publish_to: none
description: Dostafka Application.
environment: 
  sdk: '>=2.19.6 <3.0.0'

dependencies: 
  flutter:
    sdk: flutter
  firebase_core: ^3.2.0
  cupertino_icons: ^1.0.6
  get: 4.6.6
  #flutter_local_notifications: ^16.1.0
  get_storage:
  uuid: ^4.2.1
  image_picker: ^1.1.2
  flutter_image_compress: ^2.3.0
  local_auth: ^2.1.7
  easy_dialog: ^1.1.1
  easy_stepper: ^0.8.2
  expandable: ^5.0.1
#  sqflite: ^2.3.0
  image_loader: ^1.8.0
  flutter_spinkit: ^5.2.0
  visibility_detector: ^0.4.0+2
  carousel_slider: ^5.0.0
  flutter_slidable: ^3.0.1
  url_launcher: ^6.2.1
  animated_floating_buttons: ^0.0.2
  intl: ^0.19.0
  photo_view: ^0.14.0
  firebase_messaging: ^15.0.3
  overlay_support: ^2.1.0
  rxdart: ^0.27.7
  flutter_local_notifications: ^17.2.1+2
  flutter_app_badger: ^1.5.0
  flutter_html: ^3.0.0-beta.2
  permission_handler: ^11.3.0
  cached_network_image: ^3.3.1

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: ^3.0.1
  change_app_package_name: ^1.1.0
  flutter_test: 
    sdk: flutter

flutter: 
  uses-material-design: true
  assets:
    - assets/
    - assets/terms/

flutter_launcher_icons:
  android: true
  ios: true
  image_path_android: assets/icon_256.png
  image_path_ios: assets/ios/icon_256.png

flutter_launcher_name:
  name: "Dostafka"

get_cli:
  sub_folder: false


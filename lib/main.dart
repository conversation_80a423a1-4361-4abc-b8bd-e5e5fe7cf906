import 'dart:developer';

import 'package:dostafka/app/modules/global/utils/general_utils.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:permission_handler/permission_handler.dart';
import 'firebase_options.dart';
import 'generated/locales.g.dart';
import 'app/routes/app_pages.dart';
import 'app/modules/global/global_bindings.dart';
import 'package:rxdart/rxdart.dart';

// used to pass messages from event handler to the UI
final _messageStreamController = BehaviorSubject<RemoteMessage>();

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  var counterVal = GetStorage().read("counter");
  var counter = counterVal==null ? 0 : int.parse(counterVal);
  counter++;
  FlutterAppBadger.updateBadgeCount((counter));
  GetStorage().write("counter", "$counter");
  log("Counter: $counter");

  if (kDebugMode) {
    log("Handling a background message: ${message.messageId}");
    log('Message data: ${message.data}');
    log('Message notification: ${message.notification?.title}');
    log('Message notification: ${message.notification?.body}');
  }
}

String buildNotificationMessage(Map<String, dynamic> data) {

  var text = "";
  if (data['phone']!=null){
    text += "телефон: ${data['phone']}\n";
  }
  if (data['name']!=null){
    text += "клиент: ${data['name']}\n";
  }
  if (data['address']!=null){
    text += "адресс: ${data['address']}\n";
  }
  if (data['cost']!=null){
    text += "стоимость: ${data['cost']}\n";
  }
  if (data['status']!=null){
    var status = "";
    switch (data['status']){
      case "WAITING": status = LocaleKeys.order_status_WAITING.tr; break;
      case "COMING": status = LocaleKeys.order_status_COMING.tr; break;
      case "PICKED": status = LocaleKeys.order_status_PICKED.tr; break;
      case "EXECUTED": status = LocaleKeys.order_status_EXECUTED.tr; break;
      case "CANCELED": status = LocaleKeys.CANCELED.tr; break;
    }

    text += "статус: ${status.tr}";
  }
  return text;
}

void main() async {
  await GetStorage.init();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  WidgetsFlutterBinding.ensureInitialized();

  final messaging = FirebaseMessaging.instance;

  final settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (kDebugMode) {
    print('Permission granted: ${settings.authorizationStatus}');
  }


  final photosPermission = await Permission.photos.status;
  if (photosPermission.isDenied) {
    await Permission.photos.request();

  }


  // It requests a registration token for sending messages to users from your App server or other trusted server environment.
  String? token = await messaging.getToken()
      .then((value){
        if (value!=null){
          GetStorage().write("fcm_token", value);
        }
        if (kDebugMode) {
          log("FCM token: $value");
        }
        return value;
  });

  if (kDebugMode) {
    log('Registration Token=$token');
  }

  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    if (kDebugMode) {
      // log('Handling a foreground message: ${message.messageId}');
      // log('Message data: ${message.data}');
      // log('Message notification: ${message.notification?.title}');
      // log('Message notification: ${message.notification?.body}');
    }

    _messageStreamController.sink.add(message);
    GetStorage().write("ReloadRequired", "true");
    showSnackbar("${message.notification?.title}", buildNotificationMessage(message.data));
  });

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);


  runApp(
    GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: "Dostafka",
      defaultTransition: Transition.fade,
      initialBinding: GlobalBindings(),
      locale: Get.deviceLocale,
      fallbackLocale: const Locale("ru", "RU"),
      initialRoute: AppPages.INITIAL,
      translationsKeys: AppTranslation.translations,
      getPages: AppPages.routes,
      theme: ThemeData(primaryColor: Colors.green),
    ),
  );
}

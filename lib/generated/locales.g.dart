// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'en_US': Locales.en_US,
    'kz_KZ': Locales.kz_KZ,
    'ru_RU': Locales.ru_RU,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const DELIVERY = 'DELIVERY';
  static const SENDING = 'SENDING';
  static const WAITING = 'WAITING';
  static const COMING = 'COMING';
  static const PICKED = 'PICKED';
  static const EXECUTED = 'EXECUTED';
  static const CANCELED = 'CANCELED';
  static const dialogs_title = 'dialogs_title';
  static const dialogs_text = 'dialogs_text';
  static const dialogs_agree = 'dialogs_agree';
  static const dialogs_decline = 'dialogs_decline';
  static const dialogs_policy = 'dialogs_policy';
  static const dialogs_agreement = 'dialogs_agreement';
  static const notification_action = 'notification_action';
  static const notification_info = 'notification_info';
  static const notification_title = 'notification_title';
  static const notification_body = 'notification_body';
  static const common_dateFrom = 'common_dateFrom';
  static const common_dateTo = 'common_dateTo';
  static const common_archive = 'common_archive';
  static const common_free = 'common_free';
  static const common_title = 'common_title';
  static const common_date = 'common_date';
  static const common_time = 'common_time';
  static const common_phone = 'common_phone';
  static const common_password = 'common_password';
  static const common_firstName = 'common_firstName';
  static const common_lastName = 'common_lastName';
  static const common_name = 'common_name';
  static const common_city = 'common_city';
  static const common_area = 'common_area';
  static const common_authorization = 'common_authorization';
  static const common_address = 'common_address';
  static const common_promocode = 'common_promocode';
  static const common_email = 'common_email';
  static const common_language = 'common_language';
  static const common_change_password = 'common_change_password';
  static const common_delete_account = 'common_delete_account';
  static const common_delete_account_knowledge =
      'common_delete_account_knowledge';
  static const news_default_title = 'news_default_title';
  static const news_default_text = 'news_default_text';
  static const button_delete = 'button_delete';
  static const button_pay = 'button_pay';
  static const button_next = 'button_next';
  static const button_back = 'button_back';
  static const button_cancel = 'button_cancel';
  static const button_ok = 'button_ok';
  static const button_login = 'button_login';
  static const button_logout = 'button_logout';
  static const button_close = 'button_close';
  static const button_add = 'button_add';
  static const button_order = 'button_order';
  static const button_signUp = 'button_signUp';
  static const button_addPhoto = 'button_addPhoto';
  static const button_save = 'button_save';
  static const button_restore = 'button_restore';
  static const message_success_header = 'message_success_header';
  static const message_success_body = 'message_success_body';
  static const message_offline_header = 'message_offline_header';
  static const message_offline_body = 'message_offline_body';
  static const message_error_header = 'message_error_header';
  static const message_error_body = 'message_error_body';
  static const message_userExists_header = 'message_userExists_header';
  static const message_userExists_body = 'message_userExists_body';
  static const message_invalidCredential_header =
      'message_invalidCredential_header';
  static const message_invalidCredential_body =
      'message_invalidCredential_body';
  static const message_photoRequired_header = 'message_photoRequired_header';
  static const message_photoRequired_body = 'message_photoRequired_body';
  static const menu_privacy = 'menu_privacy';
  static const menu_home = 'menu_home';
  static const menu_notification = 'menu_notification';
  static const menu_refresh = 'menu_refresh';
  static const menu_restorePassword = 'menu_restorePassword';
  static const menu_expandAllOrders = 'menu_expandAllOrders';
  static const menu_on = 'menu_on';
  static const menu_off = 'menu_off';
  static const menu_order_add = 'menu_order_add';
  static const menu_order_list = 'menu_order_list';
  static const termAndConditions_title = 'termAndConditions_title';
  static const termAndConditions_agree = 'termAndConditions_agree';
  static const order_total = 'order_total';
  static const order_title = 'order_title';
  static const order_details = 'order_details';
  static const order_accept = 'order_accept';
  static const order_decline = 'order_decline';
  static const order_delete = 'order_delete';
  static const order_sent = 'order_sent';
  static const order_done = 'order_done';
  static const order_my_orders = 'order_my_orders';
  static const order_active = 'order_active';
  static const order_active_br = 'order_active_br';
  static const order_finished = 'order_finished';
  static const order_finished_br = 'order_finished_br';
  static const order_history = 'order_history';
  static const order_history_br = 'order_history_br';
  static const order_type_delivery = 'order_type_delivery';
  static const order_type_type = 'order_type_type';
  static const order_type_sending = 'order_type_sending';
  static const order_status_title = 'order_status_title';
  static const order_status_WAITING = 'order_status_WAITING';
  static const order_status_COMING = 'order_status_COMING';
  static const order_status_PICKED = 'order_status_PICKED';
  static const order_status_EXECUTED = 'order_status_EXECUTED';
  static const order_cost_title = 'order_cost_title';
  static const order_cost_adjusted = 'order_cost_adjusted';
  static const order_cost_notAdjusted = 'order_cost_notAdjusted';
  static const order_cost_adjust_title = 'order_cost_adjust_title';
  static const order_cost_adjust_body = 'order_cost_adjust_body';
  static const order_cost_filter_approved = 'order_cost_filter_approved';
  static const order_cost_filter_not_approved =
      'order_cost_filter_not_approved';
  static const order_truckNo = 'order_truckNo';
  static const order_truckProvider = 'order_truckProvider';
  static const order_comment = 'order_comment';
  static const order_currency = 'order_currency';
  static const order_from = 'order_from';
  static const order_executed = 'order_executed';
  static const order_empty = 'order_empty';
  static const order_costDescription = 'order_costDescription';
  static const order_no = 'order_no';
  static const auth_no_account = 'auth_no_account';
  static const drawer_profile = 'drawer_profile';
  static const drawer_order = 'drawer_order';
  static const drawer_orders = 'drawer_orders';
}

class Locales {
  static const en_US = {
    'DELIVERY': 'DELIVERING',
    'SENDING': 'SENDING',
    'WAITING': 'AWAIT',
    'COMING': 'ON THE WAY',
    'PICKED': 'IN PROGRESS',
    'EXECUTED': 'COMPLETED',
    'CANCELED': 'CANCELED',
    'dialogs_title': 'How we use your data',
    'dialogs_text':
        '<p><p>To improve our service and ensure reliable user identification, we request the following information:</p><ul><li><strong>First and Last Name</strong>: used for your identification in our system.</li><li><strong>Phone Number</strong>: needed to contact you if necessary.</li><li><strong>Photos</strong>: we ask you to attach photos of receipts and, optionally, products to confirm purchases and maintain the relevance of our inventory.</li></ul><p>We guarantee that this information will not be sold, transferred to third parties, or used for unsolicited communications. Your privacy and data security are our priority.</p></p>',
    'dialogs_agree': 'Accept',
    'dialogs_decline': 'Decline',
    'dialogs_policy': 'privacy policy',
    'dialogs_agreement': 'user agreement',
    'notification_action': 'Create notification',
    'notification_info': 'Select the dates when the notification will be shown',
    'notification_title': 'Title',
    'notification_body': 'Notification text',
    'common_dateFrom': 'from',
    'common_dateTo': 'to',
    'common_archive': 'Order\'s Archive',
    'common_free': 'FREE',
    'common_title': 'Dostafka',
    'common_date': 'Date',
    'common_time': 'Time',
    'common_phone': 'Phone number',
    'common_password': 'Password',
    'common_firstName': 'First name',
    'common_lastName': 'Last name',
    'common_name': 'Name',
    'common_city': 'City',
    'common_area': 'Area',
    'common_authorization': 'Authorization',
    'common_address': 'Address',
    'common_promocode': 'Reference Code',
    'common_email': 'Email',
    'common_language': 'Language',
    'common_change_password': 'Change Password',
    'common_delete_account': 'Delete Account',
    'common_delete_account_knowledge':
        'I understand deleting my account cannot be reverted',
    'news_default_title': 'Добро пожаловать!',
    'news_default_text':
        'Заказывать  достаку  посылок  теперь  стало  легко  с  приложением  Dostafka!',
    'button_delete': 'Delete',
    'button_pay': 'Pay',
    'button_next': 'Next',
    'button_back': 'Back',
    'button_cancel': 'Cancel',
    'button_ok': 'OK',
    'button_login': 'Login',
    'button_logout': 'Logout',
    'button_close': 'Close',
    'button_add': 'Add',
    'button_order': 'Order',
    'button_signUp': 'Sign up',
    'button_addPhoto': 'Add Photo',
    'button_save': 'Save',
    'button_restore': 'Restore password',
    'message_success_header': 'Completed',
    'message_success_body': 'Operation completed successfully',
    'message_offline_header': 'Offline',
    'message_offline_body':
        'Please check your internet connection and try again',
    'message_error_header': 'Error',
    'message_error_body': 'Unexpected error, please try again later',
    'message_userExists_header': 'Account already exists',
    'message_userExists_body':
        'User already registered, please try again with different phone number',
    'message_invalidCredential_header': 'Error',
    'message_invalidCredential_body': 'Incorrect credential',
    'message_photoRequired_header': 'No photo added',
    'message_photoRequired_body': 'Add a photo of your receipt',
    'menu_privacy': 'Privacy Policy',
    'menu_home': 'Home',
    'menu_notification': 'Notifications',
    'menu_refresh': 'Refresh',
    'menu_restorePassword': 'Restore password',
    'menu_expandAllOrders': 'Expand All Orders',
    'menu_on': 'ON',
    'menu_off': 'OFF',
    'menu_order_add': 'Order',
    'menu_order_list': 'My orders',
    'termAndConditions_title': 'Terms and Conditions',
    'termAndConditions_agree': 'I agree with Terms and Conditions',
    'order_total': 'Total orders',
    'order_title': 'Order',
    'order_details': 'Details',
    'order_accept': 'Accept order',
    'order_decline': 'Decline order',
    'order_delete': 'Delete order',
    'order_sent': 'Order sent',
    'order_done': 'Completed',
    'order_my_orders': 'My Orders',
    'order_active': 'Active orders',
    'order_active_br': 'Active',
    'order_finished': 'Completed orders',
    'order_finished_br': 'Completed',
    'order_history': 'Archived orders',
    'order_history_br': 'Archived',
    'order_type_delivery': 'Receive package',
    'order_type_type': 'Order type',
    'order_type_sending': 'Send package',
    'order_status_title': 'Status',
    'order_status_WAITING': 'Waiting',
    'order_status_COMING': 'Coming',
    'order_status_PICKED': 'Accepted',
    'order_status_EXECUTED': 'Executed',
    'order_cost_title': 'Cost',
    'order_cost_adjusted': 'Price approved',
    'order_cost_notAdjusted': 'Price is not adjusted yet',
    'order_cost_adjust_title': 'Change cost',
    'order_cost_adjust_body': 'Specify the cost of the service',
    'order_cost_filter_approved': 'Approved',
    'order_cost_filter_not_approved': 'Not approved',
    'order_truckNo': 'Truck number',
    'order_truckProvider': 'Carrier',
    'order_comment': 'Comment',
    'order_currency': 'tenge',
    'order_from': 'from',
    'order_executed': 'Order submitted',
    'order_empty': 'No orders',
    'order_costDescription':
        'Price is subject to change after reviewing package. The price may also include the cost of the product if it has not been paid in advance.',
    'order_no': '#',
    'auth_no_account': 'Don\'t have an account?',
    'drawer_profile': 'Profile',
    'drawer_order': 'Make an order',
    'drawer_orders': 'My orders',
  };
  static const kz_KZ = {
    'DELIVERY': 'ЖЕТКІЗУ',
    'SENDING': 'ЖІБЕРІЛІП ЖАТЫР',
    'WAITING': 'КҮТУ',
    'COMING': 'ЖОЛДА',
    'PICKED': 'ӨҢДЕУ',
    'EXECUTED': 'АЯҚТАЛДЫ',
    'CANCELED': 'ЖОЙЫЛДЫ',
    'dialogs_title': 'Деректеріңізді қалай қолданамыз',
    'dialogs_text':
        '<p><p>Біздің қызметімізді жақсарту және пайдаланушының сенімді идентификациясын қамтамасыз ету үшін, біз келесі ақпаратты сұраймыз:</p><ul><li><strong>Аты және тегі</strong>: біздің жүйемізде сізді идентификациялау үшін қолданылады.</li><li><strong>Телефон нөмірі</strong>: қажет болған жағдайда сізбен байланысу үшін.</li><li><strong>Фотосуреттер</strong>: сатып алуларды растау және біздің тауарлар ассортиментінің өзектілігін сақтау үшін, біз сізден чектердің және мүмкін болса, тауарлардың фотосуреттерін тіркеуді сұраймыз.</li></ul><p>Бұл ақпараттың сатылмайтынын, үшінші тараптарға берілмейтінін немесе сіздің келісіміңізсіз хабарламалар үшін қолданылмайтынын кепілдейміз. Сіздің құпиялығыңыз және деректер қауіпсіздігі — біздің басымдығымыз.</p></p>',
    'dialogs_agree': 'Қабылдаймын',
    'dialogs_decline': 'Бас тарту',
    'dialogs_policy': 'құпиялық саясаты',
    'dialogs_agreement': 'пайдаланушы келісімі',
    'notification_action': 'Хабарландыру жасау',
    'notification_info': 'Хабарландыру көрсетілетін күндерді таңдаңыз',
    'notification_title': 'Тақырып',
    'notification_body': 'Хабарлама мәтіні',
    'common_dateFrom': 'бастап',
    'common_dateTo': 'дейін',
    'common_archive': 'Тапсырыстар мұрағаты',
    'common_free': 'ТЕГІН',
    'common_title': 'Dostafka',
    'common_date': 'Күні',
    'common_time': 'Уақыт',
    'common_phone': 'Телефон нөмірі',
    'common_password': 'Құпия сөз',
    'common_firstName': 'Аты',
    'common_lastName': 'Тегі',
    'common_name': 'Аты',
    'common_city': 'Қала',
    'common_area': 'Аудан',
    'common_authorization': 'Авторизация',
    'common_address': 'Мекенжай',
    'common_promocode': 'Промокод',
    'common_email': 'Электрондық пошта',
    'common_language': 'Тіл',
    'common_change_password': 'Құпия сөзді өзгерту',
    'common_delete_account': 'Есептік жазбаны жою',
    'common_delete_account_knowledge':
        'Есептік жазбамды жоюды қайтару мүмкін емес екенін түсінемін',
    'news_default_title': 'Добро пожаловать!',
    'news_default_text':
        'Заказывать  достаку  посылок  теперь  стало  легко  с  приложением  Dostafka!',
    'button_delete': 'Жою',
    'button_pay': 'Төлеу',
    'button_next': 'Әрі қарай',
    'button_back': 'Артқа',
    'button_cancel': 'Болдырмау',
    'button_ok': 'ОК',
    'button_login': 'Кіру',
    'button_logout': 'Выйти',
    'button_close': 'Шығу',
    'button_add': 'Қосу',
    'button_order': 'Тапсырыс',
    'button_signUp': 'Аккаунты құру',
    'button_addPhoto': 'Фотосурет қосыңыз',
    'button_save': 'Сақтау',
    'button_restore': 'Құпия сөзді қалпына келтіріңіз',
    'message_success_header': 'Аяқталды',
    'message_success_body': 'Операция сәтті аяқталды',
    'message_offline_header': 'Интернет байланысы жоқ',
    'message_offline_body': 'Интернет байланысын тексеріп, әрекетті қайталаңыз',
    'message_error_header': 'Қате',
    'message_error_body':
        'Күтпеген сервер қатесі, әрекетті кейінірек қайталаңыз',
    'message_userExists_header': 'Пайдаланушы бұрыннан бар',
    'message_userExists_body':
        'Бұл пайдаланушы бұрыннан тіркелген, басқа мәліметтерді беріңіз',
    'message_invalidCredential_header': 'Қате',
    'message_invalidCredential_body': 'Қате телефон/пароль тіркесімі',
    'message_photoRequired_header': 'Фотосурет жоқ',
    'message_photoRequired_body': 'Түбіртектің фотосуретін қосыңыз',
    'menu_privacy': 'Құпиялылық Саясаты',
    'menu_home': 'Басты ',
    'menu_notification': 'Хабарландырулар',
    'menu_refresh': 'Жаңарту',
    'menu_restorePassword': 'Құпия сөзді қалпына келтіру',
    'menu_expandAllOrders': 'Барлық тапсырыстарды кеңейту',
    'menu_on': 'ҚОСУ',
    'menu_off': 'ӨШІРУ',
    'menu_order_add': 'Тапсырыс',
    'menu_order_list': 'Менің тапсырыстарым',
    'termAndConditions_title': 'Лицензиялық келісімнің талаптары',
    'termAndConditions_agree': 'Мен келісім шарттарын қабылдаймын',
    'order_total': 'Жалпы тапсырыстар',
    'order_title': 'Тапсырыс',
    'order_details': 'Сипаттама',
    'order_accept': 'Қабылдау',
    'order_decline': 'Қабылдамау',
    'order_delete': 'Тапсырысты жою',
    'order_sent': 'Өңделді',
    'order_done': 'Аяқталды',
    'order_my_orders': 'Менің тапсырыстарым',
    'order_active': 'Белсенді тапсырыстар',
    'order_active_br': 'Белсенді',
    'order_finished': 'Аяқталған тапсырыстар',
    'order_finished_br': 'Аяқталған',
    'order_history': 'Тапсырыстар мұрағаты',
    'order_history_br': 'Мұрағаты',
    'order_type_delivery': 'Сәлемдемені жеткізіңіз',
    'order_type_type': 'Тапсырыс түрі',
    'order_type_sending': 'Сәлемдеме жіберу',
    'order_status_title': 'Күй',
    'order_status_WAITING': 'Күтуде',
    'order_status_COMING': 'Орындалды',
    'order_status_PICKED': 'Қабылданды',
    'order_status_EXECUTED': 'Аяқталды',
    'order_cost_title': 'Бағасы',
    'order_cost_adjusted': 'Расталды',
    'order_cost_notAdjusted': 'Растау үшін',
    'order_cost_adjust_title': 'Бағаны өзгерту',
    'order_cost_adjust_body': 'Қызмет құнын көрсетіңіз',
    'order_cost_filter_approved': 'Бекітілген',
    'order_cost_filter_not_approved': 'Бекітілмеген',
    'order_truckNo': 'Көліктің нөмірі',
    'order_truckProvider': 'Тасымалдаушы',
    'order_comment': 'Пікір',
    'order_currency': 'теңге',
    'order_from': 'Бастап',
    'order_executed': 'Тапсырыс берілді',
    'order_empty': 'Тапсырыс жоқ',
    'order_costDescription':
        'Баға сәлемдемені тексеру/өлшеу кезінде өзгертілуі мүмкін. Бағаға, егер ол алдын ала төленбеген болса, өнімнің өзіндік құны да кіруі мүмкін.',
    'order_no': '№',
    'auth_no_account': 'Есептік жазбаңыз жоқ па?',
    'drawer_profile': 'Профиль',
    'drawer_order': 'Тапсырыстар жасау',
    'drawer_orders': 'Менің тапсырыстарым',
  };
  static const ru_RU = {
    'DELIVERY': 'ДОСТАВКА',
    'SENDING': 'ОТПРАВКА',
    'WAITING': 'ОЖИДАЕТ',
    'COMING': 'В ПУТИ',
    'PICKED': 'ПРИНЯТ',
    'EXECUTED': 'ВЫПОЛНЕН',
    'CANCELED': 'ОТМЕНЕН',
    'dialogs_title': 'Как мы используем ваши данные',
    'dialogs_text':
        '<p><p>Для улучшения нашего сервиса и обеспечения надёжной идентификации пользователя, мы просим предоставить следующие данные:</p><ul><li><strong>Фамилия и Имя</strong>: используются для вашей идентификации в нашей системе.</li><li><strong>Номер телефона</strong>: необходим для связи с вами в случае необходимости.</li><li><strong>Фотографии</strong>: мы просим прикладывать фотографии квитанций и, опционально, товаров для подтверждения покупок и поддержания актуальности нашего ассортимента.</li></ul><p>Мы гарантируем, что эти данные не будут проданы, переданы третьим лицам или использованы для рассылки без вашего согласия. Ваша конфиденциальность и безопасность данных — наш приоритет.</p></p>',
    'dialogs_agree': 'Принимаю',
    'dialogs_decline': 'Отказываюсь',
    'dialogs_policy': 'политика конфиденциальности',
    'dialogs_agreement': 'пользовательское соглашение',
    'notification_action': 'Создать уведомление',
    'notification_info': 'Выберите даты когда уведомление будет показываться',
    'notification_title': 'Заголовок',
    'notification_body': 'Текст уведомления',
    'common_dateFrom': 'с',
    'common_dateTo': 'по',
    'common_archive': 'Архив заказов',
    'common_free': 'Бесплатно',
    'common_title': 'Dostafka',
    'common_date': 'Дата',
    'common_time': 'Время',
    'common_phone': 'Номер телефона',
    'common_password': 'Пароль',
    'common_firstName': 'Имя',
    'common_lastName': 'Фамилия',
    'common_name': 'Имя',
    'common_city': 'Город',
    'common_area': 'Район',
    'common_authorization': 'Авторизация',
    'common_address': 'Адрес',
    'common_promocode': 'Промокод',
    'common_email': 'Электронная почта',
    'common_language': 'Язык',
    'common_change_password': 'Изменить пароль',
    'common_delete_account': 'Удалить аккаунт',
    'common_delete_account_knowledge':
        'Я понимаю, что удаление моей учетной записи невозможно отменить.',
    'news_default_title': 'Добро пожаловать!',
    'news_default_text':
        'Заказывать  достаку  посылок  теперь  стало  легко  с  приложением  Dostafka!',
    'button_delete': 'Удалить',
    'button_pay': 'Оплатить',
    'button_next': 'Далее',
    'button_back': 'Назад',
    'button_cancel': 'Отмена',
    'button_ok': 'ОК',
    'button_login': 'Войти',
    'button_logout': 'Выйти',
    'button_close': 'Закрыть',
    'button_add': 'Добавить',
    'button_order': 'Заказать',
    'button_signUp': 'Создать аккаунт',
    'button_addPhoto': 'Добавить фотографию',
    'button_save': 'Сохранить',
    'button_restore': 'Сбросить пароль',
    'message_success_header': 'Завершено',
    'message_success_body': 'Операция выполнена успешно',
    'message_offline_header': 'Интернет подключения отсутствует',
    'message_offline_body':
        'Пожалуйста проверьте интернет подключение и повторите попытку',
    'message_error_header': 'Ошибка',
    'message_error_body':
        'Непредвиденная ошибка на сервере, попробуйте повторить позже',
    'message_userExists_header': 'Пользователь уже существует',
    'message_userExists_body':
        'Данный пользователь уже зарегистрирован, укажите другие данные',
    'message_invalidCredential_header': 'Ошибка',
    'message_invalidCredential_body': 'Неверная комбинация телефон / пароль',
    'message_photoRequired_header': 'Фото отсутствует',
    'message_photoRequired_body': 'Добавьте фото квитанции',
    'menu_privacy': 'Политика Конфиденциальности',
    'menu_home': 'Главная',
    'menu_notification': 'Уведомления',
    'menu_refresh': 'Обновить',
    'menu_restorePassword': 'Восстановить пароль',
    'menu_expandAllOrders': 'Развернуть все заказы',
    'menu_on': 'ВКЛ',
    'menu_off': 'ВЫКЛ',
    'menu_order_add': 'Заказ',
    'menu_order_list': 'Мои заказы',
    'termAndConditions_title': 'Условия лицензионного соглашения',
    'termAndConditions_agree': 'Я принимаю условия соглашения',
    'order_total': 'Всего заказов',
    'order_title': 'Заказ',
    'order_details': 'Описание',
    'order_accept': 'Принять',
    'order_decline': 'Отклонить',
    'order_delete': 'Удалить',
    'order_sent': 'Обработано',
    'order_done': 'Выполнен',
    'order_my_orders': 'Мои заказы',
    'order_active': 'Активные заказы',
    'order_active_br': 'Активные',
    'order_finished': 'Выполненные заказы',
    'order_finished_br': 'Выполненные',
    'order_history': 'Архив заказов',
    'order_history_br': 'Архив',
    'order_type_delivery': 'Доставить посылку',
    'order_type_type': 'Тип заказа',
    'order_type_sending': 'Отправить посылку',
    'order_status_title': 'Статус',
    'order_status_WAITING': 'Ожидает',
    'order_status_COMING': 'Выполняется',
    'order_status_PICKED': 'Принят',
    'order_status_EXECUTED': 'Выполнен',
    'order_cost_title': 'Стоимость',
    'order_cost_adjusted': 'Подтверждена',
    'order_cost_notAdjusted': 'Уточняется',
    'order_cost_adjust_title': 'Изменить стоимость',
    'order_cost_adjust_body': 'Укажите стоимость услуги',
    'order_cost_filter_approved': 'Утверждена',
    'order_cost_filter_not_approved': 'Не утверждена',
    'order_truckNo': 'Номер машины',
    'order_truckProvider': 'Перевозчик',
    'order_comment': 'Комментарий',
    'order_currency': 'тенге',
    'order_from': 'от',
    'order_executed': 'Заказ отправлен',
    'order_empty': 'Нет заказов',
    'order_costDescription':
        'Цена может быть изменена при осмотре/взвешивании посылки. Цена так же может включать стоимость товара если он не был оплачен заранее.',
    'order_no': '№',
    'auth_no_account': 'Нет аккаунта?',
    'drawer_profile': 'Профиль',
    'drawer_order': 'Заказать',
    'drawer_orders': 'Мои заказы',
  };
}

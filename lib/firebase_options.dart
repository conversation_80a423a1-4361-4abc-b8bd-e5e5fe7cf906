import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;

// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA8kbJxnOrzJYsgw14wk2W0zwbeFY8dXKE',
    appId: '1:909250342627:android:1acb3cabba2cf65afb5fd6',
    messagingSenderId: '909250342627',
    projectId: 'dostafka-3c126',
    storageBucket: 'dostafka-3c126.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB_YasZJDvP5DgIHAe3-ZonbHVUjWSoss4',
    appId: '1:909250342627:ios:30edc9ad19cc0dacfb5fd6',
    messagingSenderId: '909250342627',
    projectId: 'dostafka-3c126',
    storageBucket: 'dostafka-3c126.appspot.com',
    iosClientId:
        '909250342627-l2sq8j2ivm5c73gepg1pjbfr8qi25fi2.apps.googleusercontent.com',
    iosBundleId: 'com.example.dostafka',
  );
}

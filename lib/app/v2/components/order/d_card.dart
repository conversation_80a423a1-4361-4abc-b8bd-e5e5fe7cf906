import 'dart:developer';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dostafka/app/modules/global/model/order_model.dart';
import 'package:dostafka/app/modules/global/utils/general_utils.dart';
import 'package:dostafka/app/v2/components/common/d_image.dart';
import 'package:dostafka/app/v2/components/common/d_text.dart';
import 'package:dostafka/app/v2/components/common/d_zoomed.dart';
import 'package:dostafka/app/v2/const/d_size.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../modules/admin/admin_controller.dart';
import '../../../modules/admin/components/order_status_dropdown.dart';
import '../../../modules/global/components/btn_widget.dart';
import '../../../modules/global/constants.dart';

class DCard extends StatelessWidget {
  final AdminOrder order;
  final AdminController controller;

  const DCard(this.order, this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    double vw = MediaQuery.of(context).size.width * 0.01;
    return Card(
      surfaceTintColor: Colors.white,
      elevation: 4,
      shadowColor: Colors.green,
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: SizedBox(
          width: 95 * vw,
          child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(border: Border(bottom: BorderSide(color: Colors.green))),
                  child: Slidable(
                    key: ValueKey(order.orderId),
                    endActionPane: _endActions(),
                    startActionPane: _startActions(),
                    child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DText(
                        "${order.orderId}",
                        label: LocaleKeys.order_no.tr,
                        labelSize: DSize.normal,
                        size: DSize.big,
                      ),
                      const SizedBox(width: 4),
                      DText(
                        "${order.orderDate}",
                        label: LocaleKeys.common_date.tr,
                        labelSize: DSize.normal,
                        size: DSize.big,
                      ),
                      const SizedBox(width: 4),
                      DText(
                        "${order.status}".tr,
                        label: LocaleKeys.order_status_title.tr,
                        labelSize: DSize.normal,
                        size: DSize.big,
                      ),
                      const SizedBox(width: 4),
                    ],
                    )
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      flex: 5,
                      child: SizedBox(
                        height: 250,
                        child: CarouselSlider(
                          items: order.imagesId.map((o) {
                            return DImage(
                              "${Globals.baseUrl}/public/api/image/$o",
                              55 * vw,
                            );
                          }).toList(),
                          options: CarouselOptions(height: 55 * vw),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      flex: 3,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height:10),
                          DText(
                            "${order.phone}",
                            label: LocaleKeys.common_phone.tr,
                            labelSize: DSize.normal,
                            size: DSize.big,
                          ),
                          const SizedBox(height: 4),
                          DZoomed(
                              zoomed: DText(
                              "${order.name}",
                              label: LocaleKeys.common_name.tr,
                              labelSize: DSize.normal,
                              size: DSize.large,
                              maxLines: 10,
                            ),
                              child: DText(
                                "${order.name}",
                                label: LocaleKeys.common_name.tr,
                                labelSize: DSize.normal,
                                size: DSize.big,
                              ),
                          ),
                          const SizedBox(height: 4),
                          DZoomed(
                              zoomed: DText(
                                "${order.address}",
                                label: LocaleKeys.common_address.tr,
                                labelSize: DSize.normal,
                                maxLines: 10,
                                size: DSize.large,
                              ),
                              child: DText(
                                "${order.address}",
                                label: LocaleKeys.common_address.tr,
                                labelSize: DSize.normal,
                                size: DSize.big,
                              )
                          ),
                          const SizedBox(height: 4),
                          DText(
                            "${order.cost}",
                            label: LocaleKeys.order_cost_title.tr,
                            labelSize: DSize.normal,
                            size: DSize.big,
                          ),
                          const SizedBox(height: 4),
                          DZoomed(
                              zoomed: DText(
                                "${order.comment}",
                                label: LocaleKeys.order_comment.tr,
                                labelSize: DSize.normal,
                                maxLines: 10,
                                size: DSize.large,
                              ),
                              child: DText(
                                "${order.comment}",
                                label: LocaleKeys.order_comment.tr,
                                labelSize: DSize.normal,
                                size: DSize.big,
                              )
                          ),
                        ],
                      ),
                    ),
                ])
          ]),
        ),
      ),
    );
  }

  ActionPane _startActions() {
    return ActionPane(
      motion: const BehindMotion(),
      extentRatio: 0.2,
      children: [
        _deleteAction()
      ],
    );
  }

  ActionPane _endActions() {
    return ActionPane(
      motion: const BehindMotion(),
      extentRatio: 0.5,
      children: [
        _editAction(),
        _updatePriceAction(),
        _phoneCallAction(),
      ],
    );
  }

  SlidableAction _deleteAction() {
    return SlidableAction(
      onPressed: order.status != 'EXECUTED' || order.status == ''
          ? (ctx) => {
        controller.delete(order.orderId)
            .whenComplete(() => controller.loadActiveOrders())}
          : null,
      borderRadius: BorderRadius.circular(4),
      autoClose: true,
      spacing: 10,
      icon: order.status != 'EXECUTED' ? Icons.delete : Icons.delete_forever_outlined,
      foregroundColor: order.status != 'EXECUTED' ? Colors.white : Colors.white54,
      backgroundColor: order.status != 'EXECUTED' ? Colors.red : Colors.grey,
    );
  }

  SlidableAction _editAction() {
    return SlidableAction(
      icon: order.status != 'EXECUTED' ? Icons.edit: Icons.edit_off,
      foregroundColor: order.status != 'EXECUTED' ? Colors.white : Colors.white54,
      backgroundColor: order.status != 'EXECUTED' ? Colors.orange : Colors.grey,
      autoClose: true,
      spacing: 5,
      borderRadius: BorderRadius.circular(4),
      onPressed: order.status != 'EXECUTED' ? _updateStatus : null,
    );
  }

  void _updateStatus(ctx) {
    showWidgetDialog(ctx,
        LocaleKeys.order_status_title.tr,
        250,
        MediaQuery.of(ctx).size.width-80,
        [
          const SizedBox(height: 25),
          OrderStatusSelect(
              order.status,
                  (expectedStatus) => controller.orderProvider.updateStatus(expectedStatus, order.orderId)
                  .whenComplete((){
                final orders = controller.activeOrders.value;
                try {
                  orders
                      .firstWhere((element) => element.orderId == order.orderId)
                      .status = expectedStatus;
                } on Exception catch (e) {
                  log("error: ${e.toString()}");
                }
                controller.activeOrders(orders);
                controller.flushed(true);
                Get.close(0);
              })
          ),
          const SizedBox(height: 25),
          TextButton(
            child: Text(LocaleKeys.button_close.tr),
            onPressed: () {
              Get.close(0);
            },
          )
        ]
    );
  }

  SlidableAction _phoneCallAction() {
    return SlidableAction(
      icon: Icons.call,
      backgroundColor: Colors.green,
      autoClose: true,
      spacing: 10,
      borderRadius: BorderRadius.circular(4),
      onPressed: (ctx) => _makingPhoneCall(order.phone),
    );
  }

  SlidableAction _updatePriceAction() {
    return SlidableAction(
      icon: Icons.attach_money_sharp,
      backgroundColor: Colors.blue,
      autoClose: true,
      spacing: 5,
      borderRadius: BorderRadius.circular(4),
      onPressed: _updatePrice,
    );
  }

  _makingPhoneCall(phone) async {
    var url = Uri.parse("tel:+7$phone");
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  _updatePrice(ctx){
    final costController = TextEditingController(text: "${order.cost}");
    controller.lastViewed(order.orderId);
    showWidgetDialog(
        ctx,
        LocaleKeys.order_cost_adjust_title.tr,
        200,
        MediaQuery.of(ctx).size.width-80,
        [
          Text(LocaleKeys.order_cost_adjust_body.tr),
          TextField(
            keyboardType: TextInputType.number,
            controller: costController,
          ),
          Btn(
            label: LocaleKeys.button_save.tr,
            onPressFunction: () {
              // cost(int.parse(costController.text));
              controller.orderProvider
                  .updateCost(costController.text, order.orderId)
                  .whenComplete((){
                final orders = controller.activeOrders.value;
                try {
                  orders
                      .firstWhere((element) => element.orderId == order.orderId)
                      .cost = int.parse(costController.text);
                } on Exception catch (e) {
                  log("error: ${e.toString()}");
                }
                controller.activeOrders(orders);
                controller.flushed(true);
                Get.close(0);
              }
              );
            },
          )
        ]
    );
  }

}
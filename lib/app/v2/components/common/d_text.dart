
import 'package:dostafka/app/v2/const/d_location.dart';
import 'package:dostafka/app/v2/const/d_size.dart';
import 'package:flutter/material.dart';

class DText extends StatelessWidget {
  final String text;
  final String? label;
  final DSize size;
  final DSize labelSize;
  final TextStyle textStyle;
  final TextStyle labelStyle;
  final DLocation labelLocation;
  final TextOverflow overflow;
  final int maxLines;
  final MainAxisAlignment mainAlignment;
  final CrossAxisAlignment crossAlignment;

  const DText(
      this.text, {
        this.label,
        this.size = DSize.normal,
        this.labelSize = DSize.tiny,
        this.labelLocation = DLocation.top,
        this.overflow = TextOverflow.fade,
        this.maxLines = 1,
        this.textStyle = const TextStyle(),
        this.labelStyle = const TextStyle(),
        this.mainAlignment = MainAxisAlignment.start,
        this.crossAlignment = CrossAxisAlignment.start,
        super.key,
      });

  TextStyle _applyTextStyle(TextStyle baseStyle, double fontSize, TextOverflow overflow) {
    return baseStyle.copyWith(fontSize: fontSize, overflow: overflow);
  }

  Widget _buildWithLabel(Widget finalText, Widget finalLabel) {

    if (labelLocation == DLocation.top || labelLocation == DLocation.bottom) {
      return Column(
        mainAxisAlignment: mainAlignment,
        crossAxisAlignment: crossAlignment,
        children: labelLocation == DLocation.top ? [finalLabel, finalText] : [finalText, finalLabel],
      );
    } else {
      return Row(
        mainAxisAlignment: mainAlignment,
        crossAxisAlignment: crossAlignment,
        children: labelLocation == DLocation.left
            ? [finalLabel, SizedBox(width: size.pixel), finalText]
            : [finalText, SizedBox(width: size.pixel), finalLabel],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final finalTextStyle = _applyTextStyle(textStyle, size.fontSize, overflow);
    final finalText = Text(text, overflow: overflow, maxLines: maxLines, style: finalTextStyle);

    if (label == null){
      return finalText;
    }

    final finalLabelStyle = _applyTextStyle(labelStyle, labelSize.fontSize, TextOverflow.clip);
    final finalLabel = Text(label!, style: finalLabelStyle);

    return _buildWithLabel(finalText, finalLabel);
  }
}
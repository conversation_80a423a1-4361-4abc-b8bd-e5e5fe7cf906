

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DZoomed extends StatelessWidget {

  final Widget child;
  final Widget zoomed;

  const DZoomed({required this.child, required this.zoomed, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onDoubleTap: () => _showObjectOverlay(context, zoomed),
      child: child,
    );
  }

  void _showObjectOverlay(BuildContext context, Widget zoomedChild) {
    Get.dialog(
      Scaffold(
        backgroundColor: Colors.black.withOpacity(0.4),
        body: Stack(
          children: [
            Positioned(
              top: MediaQuery.of(context).size.height/2 - 30,
              right: 10,
              left: 10,
              child: Card(
                    color: Colors.white,
                    child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: zoomedChild
                ),
              ) ,
            ),
            Positioned(
              top: 30,
              right: 20,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_loader/image_helper.dart';
import 'package:photo_view/photo_view.dart';
import '../../../modules/global/constants.dart';

class DImage extends StatelessWidget {
  final String imageId;
  final double size;

  const DImage(this.imageId, this.size, {super.key});

  @override
  Widget build(BuildContext context) {
    final image = _getImage(size, imageId);

    return GestureDetector(
      onDoubleTap: () => _showImageOverlay(context),
      child: image,
    );
  }

  Widget _getImage(double size, String imageId) {
    return ImageHelper(
      imageShape: ImageShape.rectangle,
      borderRadius: const BorderRadius.all(
        Radius.circular(18.0),
      ),
      width: size,
      height: size,
      boxFit: BoxFit.contain,
      image: imageId,
      imageType: ImageType.network,
      defaultLoaderColor: Globals.primeColor,
      defaultErrorBuilderColor: Colors.blueGrey,
      errorBuilder: _errorBuilderIcon,
    );
  }

  void _showImageOverlay(BuildContext context) {
    Get.dialog(
      Scaffold(
        backgroundColor: Colors.black.withOpacity(0.7),
        body: Stack(
          children: [
            Center(
              child: PhotoView(
                imageProvider: NetworkImage(imageId),
                backgroundDecoration: const BoxDecoration(
                  color: Colors.transparent,
                ),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
              ),
            ),
            Positioned(
              top: 30,
              right: 20,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _errorBuilderIcon => const Icon(
    Icons.image_not_supported,
    size: 100,
  );
}
import 'package:dostafka/app/modules/global/components/v2/password.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../global/components/btn_widget.dart';
import '../global/components/label_widget.dart';
import '../global/components/language_widget.dart';
import '../global/components/phone_widget.dart';
import '../global/constants.dart';
import 'login_controller.dart';

class LoginView extends GetView<LoginController> {
  final LoginController loginController = Get.find<LoginController>();

  LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    void login() {
      FocusScope.of(context).unfocus();
      controller.login();
    }

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        actions: null,
        leading: null,
        elevation: 5,
        toolbarHeight: 200,
        automaticallyImplyLeading: false,
        backgroundColor: Globals.primeColor,
        title: const Image(
          image: AssetImage("assets/logo2.png"),
          width: 400,
          height: 200,
        ),
      ),
      body: WillPopScope(
        onWillPop: () {
          SystemNavigator.pop(animated: true);
          return Future.value(false);
        },
        child: Padding(
            padding: const EdgeInsets.all(10),
            child: ListView(
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(10),
                    child: Label(
                      LocaleKeys.common_authorization.tr,
                      size: 20,
                      bold: true,
                    )),
                Container(
                  padding: const EdgeInsets.all(10),
                  child:
                      PhoneInput(controller: controller.phoneNumberController),
                ),
                Container(
                  padding: const EdgeInsets.all(10),
                  child: Password(
                      controller: controller.passwordController,
                      label: LocaleKeys.common_password.tr,
                      ),
                ),
                const SizedBox(height: 30),
                Obx(
                  () => controller.inProgress.value
                      ? Container(
                          alignment: Alignment.center,
                          child: const CircularProgressIndicator(
                              color: Colors.greenAccent),
                        )
                      : Container(
                          height: 50,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Btn(
                            onPressFunction:
                                controller.readyForLogin.value ? login : null,
                            label: LocaleKeys.button_login.tr,
                          )),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    const SizedBox(height: 10,),
                    Text(LocaleKeys.auth_no_account.tr),
                    TextButton(
                      child: Text(
                        LocaleKeys.button_signUp.tr,
                        style: const TextStyle(fontSize: 20),
                      ),
                      onPressed: () => Get.toNamed('/registration'),
                    )
                  ],
                ),
                Container(
                    height: 50,
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                    child: LanguageSelect(
                        languageController: controller.languageController)),
              ],
            )),
      ),
    );
  }
}

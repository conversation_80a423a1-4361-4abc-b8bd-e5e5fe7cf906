import 'dart:developer';

import 'package:flutter/material.dart';

import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../../modules/login/login_request_provider.dart';
import '../global/controllers/language_controller.dart';
import '../global/controllers/state_controller.dart';
import '../global/model/user_model.dart';
import '../global/utils/general_utils.dart';

class LoginController extends GetxController {
  final languageController = Get.find<LanguageController>();
  final stateController = Get.find<StateController>();
  final loginProvider = Get.find<LoginProvider>();

  final phoneNumberController = TextEditingController();
  final passwordController = TextEditingController();
  final phoneEntered = Rxn<String>();
  final passwordEntered = Rxn<String>();
  final inProgress = false.obs;
  final readyForLogin = false.obs;

  @override
  void onInit() {
    super.onInit();
    log("app id: ${Firebase.app().options.appId}");

    phoneNumberController.addListener(() {
      phoneEntered.value = phoneNumberController.text;
      readyForLogin(_isReadyForLogin());
    });
    passwordController.addListener(() {
      passwordEntered.value = passwordController.text;
      readyForLogin(_isReadyForLogin());
    });
  }

  bool _isReadyForLogin() {
    return phoneEntered.value != null &&
        phoneEntered.value!.isNotEmpty &&
        passwordEntered.value != null &&
        passwordEntered.value!.isNotEmpty;
  }

  void login() async {
    inProgress(true);
    loginProvider
        .login(stateController.getDeviceId(), phoneEntered.value,
            passwordEntered.value)
        .then((value) {
      if (value.code > 0) {
        if (value.code == 200 && value.payload?.token != null) {
          log("Success");
          stateController.setOffline(false);
          UserModel newUser = UserModel(
              type: value.payload?.userType ?? 'NONE',
              phone: phoneEntered.value,
              token: value.payload?.token,
              authorized: value.payload?.token != null,
              id: value.payload?.id ?? -1);
          stateController.setUser(newUser);
          passwordController.clear();
          if (value.payload?.userType == 'ADMIN'){
            Get.toNamed('/admin');
          } else {
            Get.toNamed('/home');
          }
        } else {
          showSnackbar(LocaleKeys.message_invalidCredential_header.tr,
              LocaleKeys.message_invalidCredential_body.tr);
        }
      } else {
        stateController.setOffline(true);
        showSnackbar(LocaleKeys.message_offline_header.tr,
            LocaleKeys.message_offline_body.tr);
      }
    }).whenComplete(() => inProgress(false));
  }
}

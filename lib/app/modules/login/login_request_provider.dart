import 'package:get/get.dart';

import '../global/constants.dart';
import '../global/model/login_request_model.dart';
import '../global/model/server_response.dart';
import 'package:get_storage/get_storage.dart';

class LoginProvider extends GetConnect {
  final jsonContentType = "application/json";

  @override
  void onInit() {
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return LoginResponse.fromJson(map);
      if (map is List) {
        return map.map((item) => LoginResponse.fromJson(item)).toList();
      }
    };
    httpClient.baseUrl = Globals.baseUrl;
  }

  Future<ServerResponse<LoginResponse?>> login(
          deviceId, phone, password) async =>
      callLogin(deviceId, phone, password);

  Future<ServerResponse<LoginResponse?>> callLogin(
          deviceId, phone, password) async =>
      await post('/public/login',
              {'deviceId': deviceId, 'phone': phone, 'password': password, 'fcmToken': GetStorage().read("fcm_token")},
              contentType: jsonContentType)
          .then((value) {
        return ServerResponse(
            value.isOk ? value.body : null, value.statusCode ?? -1);
      });

  Future<bool> logout(deviceId, phone) async => await post('/public/logout',
          {'deviceId': deviceId, 'phone': phone, 'password': ''},
          contentType: jsonContentType)
      .then((value) => true);
}

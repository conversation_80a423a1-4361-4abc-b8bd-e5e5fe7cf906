import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../global/constants.dart';
import 'admin_add_city_controller.dart';

class AdminAddCityView extends GetView<AdminAddCityController> {
  const AdminAddCityView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: true,
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor
      ),
      body: const Center(
        child: Text(
          'AdminAddCityView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}

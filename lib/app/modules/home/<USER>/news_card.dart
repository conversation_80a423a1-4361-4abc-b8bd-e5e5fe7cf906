import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../global/components/label_widget.dart';
import '../../global/constants.dart';

class NewsCard extends StatelessWidget {
//  final extended = false.obs;
  final String title;
  final String text;
  final String? link;
  final Image? img;
  const NewsCard({super.key, required this.title, required this.text, this.link, this.img});


  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      borderOnForeground: true,
      shadowColor: Colors.black12,
      shape: const BeveledRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(5)),
          side: BorderSide(width: .2, color: Colors.grey)),
      margin: const EdgeInsets.only(top: 5, bottom: 5, left: 15, right: 15),
      surfaceTintColor: Colors.greenAccent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(width: 15, height: 15),
              Label(title, size: 18, bold: true)
            ],
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(width: 15, height: 15),
              Expanded(child: Text(text)),
            ],
          ),
          const SizedBox(height: 5)
        ],
      ),
    );
  }

  _openLink() async {
    var url = Uri.parse(Globals.paymentUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

}

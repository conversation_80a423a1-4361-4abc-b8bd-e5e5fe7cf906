import 'package:dostafka/app/modules/global/components/v2/city_area_select.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/global/components/btn_widget.dart';
import 'package:dostafka/generated/locales.g.dart';

import '../../../global/components/input_widget.dart';
import 'user_order_controller.dart';

class UserOrderStep2View extends StatelessWidget {
  final UserOrderController userOrderController;

  const UserOrderStep2View({super.key, required this.userOrderController});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
            child: Padding(
                padding: const EdgeInsets.only(right: 15, left: 15),
                child: <PERSON>View(
                  children: [
                    Obx(
                      () => userOrderController.cities.isEmpty
                          ? const CircularProgressIndicator()
                          : CitySelect(userOrderController),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Obx(
                      () => userOrderController.areas.isEmpty
                          ? const CircularProgressIndicator()
                          : AreaSelect(userOrderController),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    TextInput(
                        controller: userOrderController.addressController,
                        label: LocaleKeys.common_address.tr,
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                  ],
                ))),
        const SizedBox(height: 25),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Btn(
              label: LocaleKeys.button_back.tr,
              onPressFunction: () => userOrderController.activeStep(0),
            ),
            const SizedBox(width: 20),
            Btn(
              label: LocaleKeys.button_next.tr,
              onPressFunction: () => userOrderController.activeStep(2),
            ),
          ],
        )
      ],
    );
  }
}

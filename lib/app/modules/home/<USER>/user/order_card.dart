import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:flutter/material.dart';

import 'package:expandable/expandable.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:image_loader/image_helper.dart';

import 'package:dostafka/generated/locales.g.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../global/constants.dart';
import '../../../global/model/order_model.dart';
import 'short_card.dart';

class OrderCard extends StatelessWidget {
  final MyOrder order;
  final _expanded = false.obs;
  final _controller = ExpandableController(initialExpanded: false);
  final UserOrderController userOrderController;

  OrderCard(
    this.order,
    this.userOrderController,
  {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    _controller.addListener(() {
      _expanded(_controller.expanded);
    });

    return Card(
      elevation: 5,
      //color: Globals.defaultBackgroundColor,
      surfaceTintColor: Globals.defaultBackgroundColor,
      child: ExpandablePanel(
        header: OrderShortCard(expanded: _expanded, order: order, userOrderController: userOrderController),
        collapsed: const SizedBox(),
        expanded: expandedOrder(context, order),
        controller: _controller,
        theme: ExpandableThemeData(
          tapBodyToExpand: true,
          tapHeaderToExpand: true,
          hasIcon: _expanded.value,
        ),
      ),
    );
  }

  Container expandedOrder(context, MyOrder o) {
    return Container(
        margin: const EdgeInsets.only(left: 10, right: 10),
        padding: const EdgeInsets.only(left: 10, right: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text("${LocaleKeys.order_title.tr} ${LocaleKeys.order_no.tr}"),
                ),
                Text("${o.orderId}")
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(LocaleKeys.common_date.tr),
                ),
                Text(o.orderDate),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(LocaleKeys.order_type_type.tr),
                ),
                Text(o.type.tr),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(LocaleKeys.common_address.tr),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width-170,
                  child: Text(o.address, softWrap: true, maxLines: 3,),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(LocaleKeys.order_cost_title.tr),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width-170,
                  child: Text(
                      "${o.cost} ${LocaleKeys.order_currency.tr}  ${!o.adjusted
                          ? '(${LocaleKeys.order_cost_notAdjusted.tr})'
                          : ''}"),
                ),
              ],
            ),
            o.comment != ""
              ? Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(LocaleKeys.order_comment.tr),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width-170,
                  child: Text(o.comment),
                )
              ],
            )
            : const SizedBox(
                height: 0,
                width: 0
            ),
            o.comment != ""
                ? Text(
              o.comment,
              softWrap: true,
              maxLines: 10,
            )
                : const SizedBox(),
            o.imagesId.isEmpty || o.status == 'EXECUTED'
                ? const SizedBox()
                : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: o.imagesId
                  .map((imageId) =>
                  ImageHelper(
                    imageShape: ImageShape.rectangle,
                    borderRadius: const BorderRadius.all(
                        Radius.circular(20.0)),
                    width:
                    MediaQuery
                        .of(context)
                        .size
                        .width,
                    //height: 300,
                    boxFit: BoxFit.fitWidth,
                    image:
                    "${Globals.baseUrl}/public/api/image/$imageId",
                    imageType: ImageType.network,
                    defaultLoaderColor: Globals.primeColor,
                    defaultErrorBuilderColor:
                    Colors.blueGrey,
                    errorBuilder: _errorBuilderIcon,
                  ))
                  .toList(),
            ),
          ],
        )
    );
  }

}

class OrderShortCard extends StatelessWidget {

  final UserOrderController userOrderController;

  const OrderShortCard({
    super.key,
    required RxBool expanded,
    required this.order,
    required this.userOrderController
  }) : _expanded = expanded;

  final RxBool _expanded;
  final MyOrder order;

  _openPayment() async {
    var url = Uri.parse(Globals.paymentUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    return
      Slidable(
        key: ValueKey(order.orderId),
          startActionPane: ActionPane(
            motion: const ScrollMotion(),
            children: [
              SlidableAction(
                foregroundColor: order.status == 'WAITING' || order.status == 'PICKED'
                    ? Colors.white
                    : Colors.black,
                backgroundColor: order.status == 'WAITING' || order.status == 'PICKED'
                    ? Colors.red
                    : Colors.grey,
                onPressed: order.status == 'WAITING' || order.status == 'PICKED'
                    ? (context){
                        userOrderController
                            .orderProvider
                            .deleteOrder(order.orderId)
                        .whenComplete((){
                          // var allOrders = userOrderController.orders.value;
                          // allOrders?.removeWhere((o) => o.orderId == order.orderId);
                          // userOrderController.orders(allOrders);
                          userOrderController.loadOrders();
                        });
                      }
                    : null,
                icon: Icons.delete,
              )
            ],
          ),
          endActionPane: ActionPane(
            motion: const ScrollMotion(),
            children: [
              SlidableAction(
                  foregroundColor: order.adjusted
                      ? Colors.white
                      : Colors.black,
                  backgroundColor: order.adjusted
                      ? Colors.green
                      : Colors.grey,
                  onPressed: order.adjusted
                      ? (ctx) => _openPayment()
                      : null,
                icon: Icons.attach_money_sharp,
                autoClose: true
              )
            ],
          ),
          child: Card(
            color: Globals.defaultBackgroundColor,
            surfaceTintColor: Globals.defaultBackgroundColor,
            margin: const EdgeInsets.only(left: 3.0, right: 5.0, top: 5.0, bottom: 3),
            //shadowColor: Colors.black12,
            elevation: 10,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8.0),
              child:  ShortCard(order: order),
            ),
          )
      );
  }

}


Widget get _errorBuilderIcon => const Icon(
  Icons.image_not_supported,
  size: 10000,
);


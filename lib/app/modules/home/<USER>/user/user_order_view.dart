import 'package:flutter/material.dart';

import 'package:easy_stepper/easy_stepper.dart';
import 'package:get/get.dart';

import 'package:dostafka/app/modules/home/<USER>/user/user_order_step4.dart';
import 'package:dostafka/generated/locales.g.dart';

import '../../../../modules/home/<USER>/user/user_order_controller.dart';
import '../../../global/components/label_widget.dart';
import '../../../global/constants.dart';
import 'user_order_step1.dart';
import 'user_order_step2.dart';
import 'user_order_step3.dart';

class UserOrderView extends StatelessWidget {
  final UserOrderController controller = Get.find<UserOrderController>();
  final Rx<String> view;
  UserOrderView({super.key, required this.view});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 10),
        Obx(() {
          switch (controller.activeStep.value) {
            case 1:
              return Label(LocaleKeys.common_address.tr, size: 20, bold: true);
            case 2:
              return Label(LocaleKeys.order_truckProvider.tr,
                  size: 20, bold: true);
            case 3:
              return Label(LocaleKeys.order_cost_title.tr,
                  size: 20, bold: true);
            default:
              return Label(LocaleKeys.order_title.tr, size: 20, bold: true);
          }
        }),
        //const SizedBox(height: 10),
        Expanded(child: Obx(() {
          switch (controller.activeStep.value) {
            case 1:
              return UserOrderStep2View(userOrderController: controller);
            case 2:
              return UserOrderStep3View(userOrderController: controller);
            case 3:
              return UserOrderStep4View(
                  userOrderController: controller, view: view);
            case 4:
              return UserOrderStep4View(
                  userOrderController: controller, view: view);
            default:
              return UserOrderStep1View(userOrderController: controller);
          }
        })),
        Obx(() => EasyStepper(
              activeStep: controller.activeStep.value,
              stepShape: StepShape.rRectangle,
              stepBorderRadius: 5,
              borderThickness: 2,
              internalPadding: 10,
              padding: const EdgeInsetsDirectional.symmetric(
                  horizontal: 30, vertical: 10),
              stepRadius: 28,
              finishedStepBorderColor: Globals.primeColor,
              finishedStepTextColor: Globals.primeColor,
              finishedStepBackgroundColor: Globals.primeColor,
              activeStepIconColor: Globals.primeColor,
              showLoadingAnimation: true,
              lineStyle: const LineStyle(
                lineLength: 20
              ),
              steps: [
                EasyStep(
                  title: LocaleKeys.order_type_type.tr,
                  enabled: true,
                  icon: const Icon(Icons.check),
                ),
                EasyStep(
                    title: LocaleKeys.common_address.tr,
                    enabled: true,
                    icon: const Icon(Icons.check)),
                EasyStep(
                    title: LocaleKeys.order_details.tr,
                    enabled: true,
                    icon: const Icon(Icons.check)),
                EasyStep(
                    title: LocaleKeys.order_cost_title.tr,
                    enabled: true,
                    icon: const Icon(Icons.check))
              ],
            ))
      ],
    );
  }
}

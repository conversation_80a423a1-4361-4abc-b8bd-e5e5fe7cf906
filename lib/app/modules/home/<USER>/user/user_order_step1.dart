
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../../../global/components/btn_widget.dart';
import 'user_order_controller.dart';

class UserOrderStep1View extends StatelessWidget {
  final UserOrderController userOrderController;

  const UserOrderStep1View({super.key, required this.userOrderController});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        //const SizedBox(height: 5),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Obx(() => RadioListTile<String>(
                title: Text(LocaleKeys.order_type_delivery.tr),
                value: 'DELIVERY',
                groupValue: userOrderController.type.value,
                onChanged: (value) => userOrderController.type(value))),
            Obx(() => RadioListTile<String>(
                title: Text(LocaleKeys.order_type_sending.tr),
                value: 'SENDING',
                groupValue: userOrderController.type.value,
                onChanged: (value) => userOrderController.type(value))),
          ],
        ),
        const SizedBox(height: 50),
        Btn(
          label: LocaleKeys.button_next.tr,
          onPressFunction: () => userOrderController.activeStep(1),
        )
      ],
    );
  }
}

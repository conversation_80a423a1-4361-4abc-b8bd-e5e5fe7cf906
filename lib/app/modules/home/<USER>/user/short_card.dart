import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../../../global/components/label_widget.dart';
import '../../../global/model/order_model.dart';

class ShortCard extends StatelessWidget {
  const ShortCard({
    super.key,
    required this.order,
  });

  final MyOrder order;

  @override
  Widget build(BuildContext context) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text("${LocaleKeys.order_no.tr} "),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    order.orderId.toString(),
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Text(
                order.orderDate,
                style: const TextStyle(color: Colors.indigo, fontSize: 12),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          (order.count != 0 && (order.count+1) %10 == 0)
            ? Label(LocaleKeys.common_free.tr)
            : priceSection(),
          const SizedBox(
            height: 20,
          ),
          Text(
            order.status.tr,
            style: const TextStyle(color: Colors.indigo, fontSize: 16),
          ),
        ]);
  }

  Column priceSection() {
    return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            order.adjusted
                ? Row(
                    children: [
                      const Icon(Icons.check,
                          size: 15, color: Colors.lightGreenAccent),
                      Text(
                        LocaleKeys.order_cost_adjusted.tr,
                        style: const TextStyle(fontSize: 11),
                      ),
                    ],
                  )
                : Row(
                    children: [
                      const Icon(Icons.warning_amber,
                          size: 15, color: Colors.amber),
                      Text(LocaleKeys.order_cost_notAdjusted.tr,
                          style: const TextStyle(fontSize: 11)),
                    ],
                  ),
            Label(
              "${order.cost} ${LocaleKeys.order_currency.tr}",
              size: 22,
              bold: true,
            ),
          ],
        );
  }
}

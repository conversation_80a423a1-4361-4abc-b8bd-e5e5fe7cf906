import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../../global/components/btn_widget.dart';
import '../../../global/components/label_widget.dart';
import 'user_order_controller.dart';

class UserOrderStep4View extends StatelessWidget {
  final UserOrderController userOrderController;
  final Rx<String> view;
  const UserOrderStep4View(
      {super.key, required this.userOrderController, required this.view});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 25),
        // Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        //   Padding(
        //     padding: const EdgeInsets.all(10),
        //     child: Label(
        //       LocaleKeys.order_cost_title.tr,
        //       size: 20,
        //       bold: true,
        //     ),
        //   )
        // ]),
        const SizedBox(height: 15),
        Obx(
          () => userOrderController.showResult.value && (userOrderController.order.value!.count+1) % 10 == 0
            ? Label("Free ${(userOrderController.order.value!.count+1)}")
            : Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                " ${LocaleKeys.order_from.tr} ",
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
              ),
              Text(
                " ${userOrderController.getCost()} ",
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 48),
              ),
              Text(
                " ${LocaleKeys.order_currency.tr} ",
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 28),
              )
            ],
          ),
        ),
        const SizedBox(height: 30),
        Obx(
          () => userOrderController.showResult.value
              ? const SizedBox()
              :  SizedBox(
            height: 115,
            child: Card(
              surfaceTintColor: Colors.white,
              color: Colors.amberAccent,
              elevation: 5,
              shadowColor: Colors.green,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(15),
              child: Row(
                children: [
                  const SizedBox(
                    width: 50,
                    child: Icon(Icons.info_rounded, color: Colors.red) ,
                  )
                  ,
                  SizedBox(
                    width: 270,
                    height: 109,
                    child: Text(LocaleKeys.order_costDescription.tr, overflow: TextOverflow.ellipsis, maxLines: 10,),
                  )
                ],
                // leading: ,
                // trailing: ,
                // contentPadding: EdgeInsets.only(left: 15, right: 15, top: 5, bottom: 2),
              ),
            ),
          ),
        ),
        const SizedBox(height: 30),
        Obx(() => userOrderController.showResult.value
            ? Column(
                children: [
                  const SizedBox(height: 10),
                  Label(
                    LocaleKeys.order_executed.tr,
                    size: 20,
                  ),
                  const SizedBox(height: 10),
                  Label(
                    "${LocaleKeys.order_no.tr} ${userOrderController.order.value?.orderId.toString()}",
                    size: 24,
                  ),
                  const SizedBox(height: 15),
                  Btn(
                      onPressFunction: () =>
                          userOrderController.closeOrder(view),
                      label: LocaleKeys.button_close.tr,
                  )
                ],
              )
            : userOrderController.orderInProgress.value
                ? const CircularProgressIndicator()
                : Btn(
                    onPressFunction: () =>
                        userOrderController.sendOrder(context),
                    label: LocaleKeys.menu_order_add.tr)),
        const SizedBox(height: 25),
        Obx(() => userOrderController.showResult.value
            ? const SizedBox()
            : TextButton(
                child: Text(
                  LocaleKeys.button_back.tr,
                  style: const TextStyle(fontSize: 20),
                ),
                onPressed: () => userOrderController.activeStep(2),
              ))
      ],
    )
    );
  }
}

import 'dart:developer';

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:dostafka/generated/locales.g.dart';
import '../../../global/components/label_widget.dart';
import 'order_card.dart';
import 'user_order_controller.dart';

class UserOrderListView extends StatelessWidget {
  final UserOrderController controller = Get.find<UserOrderController>();

  UserOrderListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(
          height: 10,
        ),
        Label(
          LocaleKeys.order_my_orders.tr,
          size: 20,
          bold: true,
        ),
        const SizedBox(height: 5),
        Expanded(
          child: RefreshIndicator(
            triggerMode: RefreshIndicatorTriggerMode.anywhere,
            onRefresh: () {
              log("Last REFRESH: ${controller.lastRefresh.value}");
              return controller.orderProvider.getMyOrders()
                  .then((value) {
                    if (value != null) {
                      controller.orders(value);
                    }
                  });
            },
            child: VisibilityDetector(
                key: const Key('singleChildScrollViewKey'),
                onVisibilityChanged: (VisibilityInfo info) {
                log("Current time: ${DateTime.now()}, Last refresh happened ${controller.lastRefresh.value}");
                final duration1 = Duration(milliseconds: (DateTime.now().millisecondsSinceEpoch - controller.lastRefresh.value.millisecondsSinceEpoch).abs()).inSeconds;
                log("Duration from last refresh : $duration1");
                if (duration1 > 60 || GetStorage().read("ReloadRequired")!=null){
                    controller.lastRefresh(DateTime.now());
                    controller.loadOrders();
                    GetStorage().remove("ReloadRequired");
                   }
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Obx(() => controller.orders.value != null &&
                            controller.orders.value!.isNotEmpty
                        ? Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: controller.orders.value!
                                .map((o) => OrderCard(o, controller))
                                .toList(),
                          )
                        : Text(LocaleKeys.order_empty.tr)),
                  ),
                )),
          ),
        )
      ],
    );
  }
}

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/global/components/btn_widget.dart';

import '../../../../../generated/locales.g.dart';
import '../../../global/components/button_widget.dart';
import '../../../global/utils/general_utils.dart';
import '../../../global/constants.dart';
import 'user_order_controller.dart';

class UserOrderStep3View extends StatelessWidget {
  final UserOrderController userOrderController;

  const UserOrderStep3View({super.key, required this.userOrderController});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.only(right: 15, left: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Column(mainAxisAlignment: MainAxisAlignment.start, children: [
                const SizedBox(height: 25),
                TextFormField(
                  decoration: InputDecoration(
                    focusedBorder: const UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.white70, width: 2),
                    ),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Colors.lightGreen, width: 2),
                    ),
                    border: const UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.white70, width: 2),
                    ),
                    labelText: LocaleKeys.order_comment.tr,
                    fillColor: Globals.fillColor,
                    filled: true,
                  ),
                  controller: userOrderController.commentController,
                  minLines: 4,
                  maxLines: 6,
                  maxLength: 2048,
                  keyboardType: TextInputType.text,
                ),
                const SizedBox(height: 15),
                Button(
                    onPressFunction: () => userOrderController.pickImage(),
                    label: LocaleKeys.button_addPhoto.tr),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Btn(
                      label: LocaleKeys.button_back.tr,
                      onPressFunction: () => userOrderController.activeStep(1),
                    ),
                    const SizedBox(width: 20),
                    Btn(
                      label: LocaleKeys.button_next.tr,
                      onPressFunction: () => userOrderController.files.isEmpty
                          ? showSnackbar(LocaleKeys.message_photoRequired_header.tr, LocaleKeys.message_photoRequired_body.tr)
                          : userOrderController.activeStep(3),
                    ),
                  ],
                ),
                Obx(() => (userOrderController.files.isEmpty)
                    ? const SizedBox()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: userOrderController.files
                            .map((f) => Card(
                                  surfaceTintColor: Colors.white,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      SizedBox(
                                        width: 120,
                                        child: Text(f.key.name,
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis),
                                      ),
                                      f.value != null
                                          ? Image.memory(f.value!, width: 165,)
                                          : const SizedBox(),
                                      IconButton(
                                          onPressed: () => userOrderController
                                              .removeImage(f.key.name),
                                          icon: const Icon(Icons.remove_circle))
                                    ],
                                  ),
                                ))
                            .toList(),
                      )),
                const SizedBox(height: 15),
              ]),
            ],
          ),
        ));
  }
}

import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../generated/locales.g.dart';
import '../../../global/controllers/language_controller.dart';
import '../../../global/controllers/state_controller.dart';
import '../../../global/model/address_model.dart';
import '../../../global/model/order_model.dart';
import '../../providers/city_provider.dart';
import '../../providers/order_provider.dart';

class UserOrderController extends GetxController {
  final activeStep = 0.obs;
  final type = 'DELIVERY'.obs;
  final area =
      Area(id: "0e56c92e-6394-4b83-9d38-4e938f70cfed", nameRu: "Other", nameKz: "Другой", nameEn: "Другой", cost: 2000, ).obs;
  final city =
      City(id: "392b6216-be8a-4054-b9aa-088acb27b0a7", nameEn: "Karaganda", nameKz: "Қарағанды", nameRu: "Караганда", cost: 2000, areas: []).obs;
  final cities = <City>[City(id: "392b6216-be8a-4054-b9aa-088acb27b0a7", nameEn: "Karaganda", nameKz: "Қарағанды", nameRu: "Караганда", cost: 2000, areas: [])].obs;
  final areas = <Area>[
    Area(id: "4e30aa1b-fe46-4e5d-85f6-3d664e443e8f", nameRu: "Город", nameKz: "Қала", nameEn: "City", cost: 1500),
    Area(id: "4a7eda3f-cbd2-4092-9769-6ac1348f3f32", nameRu: "Юг", nameKz: "Оңтүстік", nameEn: "South", cost: 1500),
    Area(id: "117c71f2-202d-4a98-84be-e99336143ab5", nameRu: "Пришахтинск", nameKz: "Пришахтинск", nameEn: "Prishakhtinsk", cost: 2000),
    Area(id: "a2817506-1069-40c6-808d-59e3fe589c6a", nameRu: "Майкудук", nameKz: "Майқұдық", nameEn: "Maykuduk", cost: 2000),
    Area(id: "011ec06a-1442-40de-a531-e209fd2168cc", nameRu: "Сортировка", nameKz: "Сортировка", nameEn: "Sorting Station", cost: 2000),
    Area(id: "0e56c92e-6394-4b83-9d38-4e938f70cfed", nameRu: "Other", nameKz: "Другой", nameEn: "Другой", cost: 2000)
  ].obs;
  final showImagePicker = false.obs;
  final files = <MapEntry<XFile, Uint8List?>>[].obs;
  File? selectedImage;
  final order = Rxn<SubmittedOrder>();
  final orderInProgress = false.obs;
  final showResult = false.obs;
  final orders = Rxn<List<MyOrder>>(null);
  final lastRefresh = Rx<DateTime>(DateTime.now());

  final languageController = Get.find<LanguageController>();
  final stateController = Get.find<StateController>();
  final cityProvider = Get.find<CityProvider>();
  final orderProvider = Get.find<OrderProvider>();
  final storage = GetStorage();

  final addressController = TextEditingController();
  final commentController = TextEditingController();
  final phoneController = TextEditingController();
  final priceController = TextEditingController();
  final truckController = TextEditingController();
  final transporterPhoneController = TextEditingController();

  void onCitySelected(City? city) {
    if (city != null) {
      this.city.value = city;
      areas.value = city.areas;
      area.value = areas[0];
      log("Area!: ${city.nameRu}");
    }
  }

  void onAreaSelected(Area? area) {
    if (area != null) {
      this.area.value = area;
      log("Area!: ${area.nameRu}");
    }
  }

  String getCityName(City city) {
    if (languageController.getLocale?.languageCode == "en") {
      return city.nameEn;
    }
    if (languageController.getLocale?.languageCode == "kz") {
      return city.nameKz;
    }
    return city.nameRu;
  }

  String getAreaName(Area area) {
    if (languageController.getLocale?.languageCode == "en") {
      return area.nameEn;
    }
    if (languageController.getLocale?.languageCode == "kz") {
      return area.nameKz;
    }
    return area.nameRu;
  }

  Future<void> pickImage() async {
    final pickedFiles = await ImagePicker().pickMultiImage(maxHeight: 1900, maxWidth: 1900, imageQuality: 80);
    pickedFiles.forEach((pickedFile) async {
      if (!files.any((element) => element.key.name == pickedFile.name)) {
        var compressedFile = await pickedFile.readAsBytes();
        files.add(MapEntry(pickedFile, compressedFile));
      }
    });
    update();
  }

  removeImage(String name) {
    files.removeWhere((element) => element.key.name == name);
  }

  int getCost() {
    return city.value.cost + area.value.cost;
  }

  void sendOrder(BuildContext context) async {
    orderInProgress(true);
    orderProvider
        .sendOrder(
            cityId: city.value.id,
            areaId: area.value.id,
            phone: phoneController.value.text != ''
                ? phoneController.value.text
                : stateController.getUser().phone,
            address: addressController.value.text,
            truckPhone: transporterPhoneController.value.text,
            truckNumber: truckController.value.text,
            comment: commentController.value.text,
            initialCost: city.value.cost + area.value.cost,
            clientId: stateController.getUser().id,
            orderType: type.value,
            images: files.map((element) => element.key))
        .then((value) {
      if (value != null) {
        order(value);
        final newOrder = MyOrder(
            orderId: value.orderId,
            orderDate: value.orderDate,
            status: LocaleKeys.order_status_WAITING,
            cost: city.value.cost + area.value.cost,
            address: addressController.value.text,
            comment: commentController.value.text,
            truckNumber: truckController.value.text,
            type: type.value,
            count: value.count,
            imagesId: value.imagesId,
            adjusted: false);
        if (orders.value != null) {
          orders.value?.insert(0, newOrder);
        } else {
          orders([newOrder]);
        }
        showResult(true);
        activeStep(4);
      } else {
        Get.showSnackbar(GetSnackBar(
          title: LocaleKeys.message_error_header.tr,
          message: LocaleKeys.message_error_body.tr,
          duration: const Duration(seconds: 4),
        ));
      }
    }).whenComplete(() => orderInProgress(false));
  }

  Future<AdminOrder?> sendAdminOrder(BuildContext context) async {
    orderInProgress(true);
    var result = await orderProvider.sendOrder(
        cityId: city.value.id,
        areaId: area.value.id,
        phone: phoneController.value.text,
        address: addressController.value.text,
        truckPhone: transporterPhoneController.value.text,
        truckNumber: truckController.value.text,
        comment: commentController.value.text,
        initialCost: priceController.value.text,
        clientId: stateController.getUser().id,
        orderType: type.value,
        images: files.map((element) => element.key))
        .then((value) {
      if (value != null) {
        var result = AdminOrder(
            orderId: value.orderId,
            orderDate: value.orderDate,
            status: LocaleKeys.order_status_PICKED,
            cost: int.parse(priceController.value.text),
            adjusted: true,
            cityArea: "${city.value.nameRu}-${area.value.nameRu}",
            address: addressController.value.text,
            phone: phoneController.value.text,
            comment: "",
            name: value.name,
            truckNumber: truckController.value.text,
            type: 'DELIVERY',
            truckPhone: '',
            count: value.count,
            imagesId: value.imagesId,
        );
        showImagePicker(false);
        files(<MapEntry<XFile, Uint8List?>>[]);
        selectedImage = null;
        order(null);
        addressController.clear();
        commentController.clear();
        phoneController.clear();
        truckController.clear();
        transporterPhoneController.clear();
        priceController.clear();
        return result;
      }
    });
    return result;
  }

  closeOrder(Rx<String> view) {
    showImagePicker(false);
    files(<MapEntry<XFile, Uint8List?>>[]);
    selectedImage = null;
    order(null);
    if (addressController.text != ''){
      storage.write('address', addressController.text);
    }
    commentController.clear();
    phoneController.clear();
    truckController.clear();
    transporterPhoneController.clear();
    activeStep(0);
    showResult(false);
    view("ORDER_LIST");
  }

  loadOrders() {
      orderProvider.getMyOrders().then((value) => {
        if (value != null) {orders(value)}
      });
  }

  @override
  void onReady() {
    cityProvider.getCities().then((value) {
      if (value != null) {
        cities(value);
        city.value = cities.where((c) => c.nameRu == 'Караганда').single;
        areas.value = city.value.areas;
        area.value = city.value.areas[0];
        stateController.setCities(value);
      } else if (cities.isEmpty) {
        cities(stateController.getCities());
      }
    });
    loadOrders();
    if (storage.hasData("address")){
      var address = storage.read("address");
      if (address != null && address != ''){
        addressController.text = address;
      }
    }
  }
}

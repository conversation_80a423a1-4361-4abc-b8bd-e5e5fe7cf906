// import 'dart:ffi';

import 'package:get/get.dart';

import '../../global/basic_api.dart';
import '../news_response_model.dart';

class NewsResponseProvider extends BasicAPI {
  NewsResponseProvider() : super();

  @override
  void onReady() {
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return NewsResponse.fromJson(map);
      if (map is List) {
        return map.map((item) => NewsResponse.fromJson(item)).toList();
      }
    };
  }

  Future<List<NewsResponse>?> getNews() async {
    try {
      updateHeaders();
      final response = await get('/api/news');
      switch (response.statusCode) {
        case 200:
          final List<dynamic> responseBody = response.body;
          final List<NewsResponse> newsList =
              responseBody.map((item) => NewsResponse.fromJson(item)).toList();
          return newsList;
        case 401:
        case 403:
          var user = stateController.getUser();
          user.token = null;
          user.authorized = false;
          stateController.setUser(user);
          Get.toNamed('/login');
          return null;
      }
    } catch (e) {
      // Handle any exceptions here
      return null;
    }
    return null;
  }

  Future<bool> postNews(NotificationRequest request) async {
    updateHeaders();
    return await post('/api/news', request.toJson())
        .then((value){
      return value.isOk;
    });
  }

  Future<Response<void>> deleteNews(int id) async =>
      await delete('/api/news/$id');
}

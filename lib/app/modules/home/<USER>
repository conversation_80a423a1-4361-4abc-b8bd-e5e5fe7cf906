import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../modules/global/constants.dart';
import '../global/controllers/state_controller.dart';
import 'components/bottom_bars.dart';
import '../global/components/drawer.dart';
import 'components/user/user_order_list.dart';
import 'components/user/user_order_view.dart';
import 'components/user_home.dart';
import 'home_controller.dart';
import 'providers/news_response_provider.dart';

class HomeView extends GetView<HomeController> {
  HomeView({super.key});
  final stateController = Get.find<StateController>();
  final newsResponseProvider = Get.find<NewsResponseProvider>();
  final userOrderController = Get.find<UserOrderController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            automaticallyImplyLeading: true,
            title:
                const Image(width: 100, image: AssetImage("assets/logo2.png")),
            centerTitle: true,
            backgroundColor: Globals.primeColor),
        drawer: UserDrawer(homeController: controller),
        body: WillPopScope(
            onWillPop: () {
              SystemNavigator.pop(animated: true);
              return Future.value(false);
            },
            child: SafeArea(
              maintainBottomViewPadding: true,
              child: Container(
                alignment: Alignment.topCenter,
                child: Obx(() {
                  switch (controller.view.value) {
                    case 'ORDER_NEW':
                      return UserOrderView(view: controller.view);
                    case 'ORDER_LIST':
                      return UserOrderListView();
                    default:
                      return UserHome(
                              controller: controller,
                              newsResponseProvider: newsResponseProvider,
                            );
                  }
                }),
              ),
            )),
        bottomNavigationBar: UserBottomNavBar(controller: controller)
    );
  }

}

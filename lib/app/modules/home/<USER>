import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../../modules/global/controllers/state_controller.dart';
import '../../modules/home/<USER>';
import '../../modules/login/login_request_provider.dart';
import '../home/<USER>/news_response_provider.dart';

class HomeController extends GetxController {
  final stateController = Get.find<StateController>();
  final newsResponseProvider = Get.find<NewsResponseProvider>();
  final loginProvider = Get.find<LoginProvider>();
  final RxString view;
  final requestInProgress = false.obs;
  final lastRefresh = Rx<DateTime>(DateTime.now());
  final Rx<List<NewsResponse>> news = Rx<List<NewsResponse>>(
      [NewsResponse(title: LocaleKeys.news_default_title.tr, text: LocaleKeys.news_default_text.tr)]);

  HomeController(this.view);

  @override
  void onInit() {
    super.onInit();
    if (stateController.role.value == 'ADMIN') {
      view('ORDER_TODAY_NEW');
    } else {
      view('HOME');
    }

  }

  @override
  void onReady() {
    loadNotifications();
  }

  void loadNotifications() {
    newsResponseProvider.getNews().then((value) {
      if (value != null) {
        value.sort( (a,b)=> b.id! - a.id!);
        news(value);
      }
    });
  }

  void logout() {
    var user = stateController.getUser();
    loginProvider.logout(stateController.getDeviceId(), user.phone);
    user.authorized = false;
    user.token = null;
    user.phone = null;
    stateController.setUser(user);

    Get.toNamed('/login');
  }


}

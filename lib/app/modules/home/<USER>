
class NewsResponse {
  int? id;
  DateTime? startDate;
  DateTime? endDate;
  String? title;
  String? text;

  NewsResponse({this.id, this.startDate, this.endDate, this.title, this.text});

  NewsResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate =
        DateTime.fromMillisecondsSinceEpoch(json['startDate'], isUtc: true);
    endDate = DateTime.fromMillisecondsSinceEpoch(json['endDate'], isUtc: true);
    title = json['title'];
    text = json['text'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['startDate'] = startDate?.microsecondsSinceEpoch;
    data['endDate'] = endDate?.microsecondsSinceEpoch;
    data['title'] = title;
    data['text'] = text;
    return data;
  }
}

class NotificationRequest{
  String? startDate;
  String? endDate;
  String? title;
  String? text;

  NotificationRequest({this.startDate, this.endDate, this.title, this.text});

  NotificationRequest.fromJson(Map<String, dynamic> json){
    startDate = json['startDate'];
    endDate = json['endDate'];
    title = json['title'];
    text = json['text'];
  }

  Map<String, dynamic> toJson(){
    final data = <String, dynamic>{};
    data['startDate'] = startDate;
    data['endDate'] = endDate;
    data['title'] = title;
    data['text'] = text;
    return data;
  }


}
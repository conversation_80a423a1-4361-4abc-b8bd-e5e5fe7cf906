import 'package:get/get.dart';

import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:dostafka/app/modules/home/<USER>/city_provider.dart';
import 'package:dostafka/app/modules/home/<USER>/news_response_provider.dart';
import 'package:dostafka/app/modules/home/<USER>/order_provider.dart';
import 'package:dostafka/app/modules/login/login_request_provider.dart';

import 'home_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    final view = 'HOME'.obs;
    Get.put<OrderProvider>(OrderProvider());
    Get.lazyPut<HomeController>(() => HomeController(view));
    Get.lazyPut<UserOrderController>(() => UserOrderController());
    Get.lazyPut<NewsResponseProvider>(() => NewsResponseProvider());
    Get.lazyPut<CityProvider>(() => CityProvider());
    Get.lazyPut<LoginProvider>(() => LoginProvider());
  }
}

import 'package:get/get.dart';

import '../../global/basic_api.dart';
import '../../global/model/address_model.dart';

class CityProvider extends BasicAPI {
  CityProvider() : super();

  @override
  void onReady() {
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return City.fromJson(map);
      if (map is List) {
        return map.map((item) => City.fromJson(item)).toList();
      }
    };
  }

  Future<List<City>?> getCities() async {
    try {
      super.updateHeaders();
      final response = await get('/api/dictionary/cities');
      switch (response.statusCode) {
        case 200:
          final List<dynamic> responseBody = response.body;
          final List<City> newsList =
              responseBody.map((item) => City.fromJson(item)).toList();
          return newsList;
        case 401:
        case 403:
          var user = stateController.getUser();
          user.token = null;
          user.authorized = false;
          stateController.setUser(user);
          Get.toNamed('/login');
          return null;
      }
    } catch (e) {
      // Handle any exceptions here
      return null;
    }
    return null;
  }
}

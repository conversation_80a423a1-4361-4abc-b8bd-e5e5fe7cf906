import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/home/<USER>/news_card.dart';
import 'package:dostafka/app/modules/home/<USER>/news_response_provider.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../modules/home/<USER>';
import '../../global/components/label_widget.dart';

class UserHome extends StatelessWidget {
  final HomeController controller;
  final NewsResponseProvider newsResponseProvider;

  const UserHome({
    super.key,
    required this.controller,
    required this.newsResponseProvider,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() =>
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            Label(LocaleKeys.menu_notification.tr, size: 20, bold: true),
            const SizedBox(height: 20),
            Expanded(
              child:
                  VisibilityDetector(
                    key: const ValueKey('NewsListView'),
                    onVisibilityChanged: (info){
                      final duration1 = Duration(milliseconds: (DateTime.now().millisecondsSinceEpoch - controller.lastRefresh.value.millisecondsSinceEpoch).abs()).inSeconds;
                      if (duration1 > 60){
                        controller.lastRefresh(DateTime.now());
                        controller.loadNotifications();
                      }
                    },
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                           Image(
                            image: const AssetImage("assets/background_2.png"),
                            width: MediaQuery.of(context).size.width-40,
                          ),
                          ...controller.news.value
                            .map(
                              (n) => NewsCard(title: n.title!, text: n.text!),
                        )
                            ],
                      ),
                    ),
                  ),
            ),
          ],
        ));
  }
}

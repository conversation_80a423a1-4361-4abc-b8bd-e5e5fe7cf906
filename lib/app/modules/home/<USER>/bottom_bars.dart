import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/home/<USER>';

import '../../../../generated/locales.g.dart';
import '../../admin/admin_controller.dart';
import '../../global/components/icon_bottom_bar.dart';
import '../../global/constants.dart';

class UserBottomNavBar extends StatelessWidget {
  final HomeController controller;
  const UserBottomNavBar({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: Globals.primeColor,
      child: SizedBox(
        height: 70,
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding: const EdgeInsets.only(left: 20.0, right: 20.0, bottom: 5),
          child: Obx(() => Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  IconBottomBar(
                      text: LocaleKeys.menu_home.tr,
                      icon: Icons.home,
                      selected: controller.view.value == 'HOME',
                      onPressed: () => controller.view('HOME')),
                  IconBottomBar(
                      text: LocaleKeys.menu_order_add.tr,
                      icon: Icons.add_to_photos_outlined,
                      selected: controller.view.value == 'ORDER_NEW',
                      onPressed: () => controller.view('ORDER_NEW')),
                  IconBottomBar(
                      text: LocaleKeys.menu_order_list.tr,
                      icon: Icons.table_view,
                      selected: controller.view.value == 'ORDER_LIST',
                      onPressed: () => controller.view('ORDER_LIST')),
                  // IconBottomBar(
                  //     text: LocaleKeys.menu_notification.tr,
                  //     icon: Icons.notification_important,
                  //     selected: controller.view.value == 'NOTIFICATIONS',
                  //     onPressed: ()=> controller.view('NOTIFICATIONS'))
                ],
              )),
        ),
      ),
    );
  }
}

class AdminBottomNavBar extends StatelessWidget {
  final AdminController controller;
  const AdminBottomNavBar(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: Globals.primeColor,
      child: SizedBox(
        height: 70,
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding: const EdgeInsets.only(left: 30.0, right: 30.0, bottom: 5),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(() => IconBottomBar(
                  text: LocaleKeys.order_active_br.tr,
                  icon: Icons.indeterminate_check_box_outlined,
                  selected: controller.view.value == 'ORDER_TODAY_NEW',
                  onPressed: () => controller.view('ORDER_TODAY_NEW'))),
              Obx(() => IconBottomBar(
                  text: LocaleKeys.order_finished_br.tr,
                  icon: Icons.check_box_outlined,
                  selected: controller.view.value == 'ORDER_TODAY_DONE',
                  onPressed: () => controller.view('ORDER_TODAY_DONE'))),
              Obx(() => IconBottomBar(
                  text: LocaleKeys.order_history_br.tr,
                  icon: Icons.table_view,
                  selected: controller.view.value == 'ORDER_HISTORY',
                  onPressed: () => controller.view('ORDER_HISTORY'))),
              // Obx(() => IconBottomBar(
              //     text: LocaleKeys.menu_notification.tr,
              //     icon: Icons.notification_important,
              //     selected: controller.view.value == 'NOTIFICATION',
              //     onPressed: () => controller.view('NOTIFICATION'))),
            ],
          ),
        ),
      ),
    );
  }
}

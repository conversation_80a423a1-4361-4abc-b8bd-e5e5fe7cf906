import 'dart:developer';
import 'dart:typed_data';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';

import 'package:dostafka/app/modules/global/model/order_model.dart';

import '../../global/basic_api.dart';

class OrderProvider extends BasicAPI {
  OrderProvider() : super();

  @override
  void onReady() {
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return AdminOrder.fromJson(map);
      if (map is List) {
        return map.map((item) => AdminOrder.fromJson(item)).toList();
      }
    };
  }

  Future<SubmittedOrder?> sendOrder({
        cityId, areaId, phone, address, truckPhone, truckNumber,
        comment, initialCost, clientId, orderType, images
      }) async {
    try {
      super.updateHeaders();

      List<MapEntry<XFile, Uint8List>> compressedList = [];
      for (XFile image in images) {
        Uint8List? compressedImage =
            await FlutterImageCompress.compressWithFile(
          image.path,
          minWidth: 1024,
          minHeight: 1024,
          quality: 25,
        );
        if (compressedImage != null) {
          compressedList.add(MapEntry(image, compressedImage));
        }
      }
      final form = FormData({
        'order': "{"
            "\"clientId\":\"$clientId\","
            "\"cityId\":\"$cityId\","
            "\"areaId\":\"$areaId\","
            "\"address\":\"$address\","
            "\"phone\":\"$phone\","
            "\"comment\":\"$comment\","
            "\"truckNumber\":\"$truckNumber\","
            "\"truckPhone\":\"$truckPhone\","
            "\"orderType\":\"${orderType.toUpperCase()}\","
            "\"initialCost\":\"$initialCost\"}",
        'fcmToken': stateController.getFcmToken(),
        'images': compressedList
            .map((entry) => MultipartFile(entry.value,
                filename: entry.key.name,
                contentType: 'application/octet-stream'))
            .toList()
      });
      final response =
          await post("/api/order", form, contentType: 'multipart/form-data');
      switch (response.statusCode) {
        case 200:
          log(response.body.toString());
          var submittedOrder = SubmittedOrder.fromJson(response.body);
          final profile = await get("/api/profile/${stateController.clientId}");
          log("${profile.bodyString}");
          return submittedOrder;
        case 401:
        case 403:
          var user = stateController.getUser();
          user.token = null;
          user.authorized = false;
          stateController.setUser(user);
          Get.toNamed('/login');
          return null;
      }
      return null;
    } catch (e) {
      // Handle any exceptions here
      return null;
    }
  }

  Future<List<MyOrder>?> getMyOrders() async {
    updateHeaders();
    var counter = GetStorage().read("counter");
    log("Loading orders! Counter: $counter");
    FlutterAppBadger.removeBadge();
    GetStorage().remove("counter");
    final response = await get("/api/order/${stateController.clientId}");
    switch (response.statusCode) {
      case 200:
        List<MyOrder> res = (response.body as List)
            .map((o) => MyOrder.fromJson(o as Map<String, dynamic>))
            .toList();
        return res;
      case 401:
      case 403:
        var user = stateController.getUser();
        user.token = null;
        user.authorized = false;
        stateController.setUser(user);
        Get.toNamed('/login');
        return null;
    }
    return null;
  }

  Future<List<AdminOrder>?> getActive() async {
    updateHeaders();
    var counter = GetStorage().read("counter");
    log("Loading orders! Counter: $counter");
    FlutterAppBadger.removeBadge();
    GetStorage().remove("counter");
    final response = await get("/api/order/active");
    if (response.isOk) {
      return (response.body as List)
          .map((o) => AdminOrder.fromJson(o as Map<String, dynamic>))
          .toList();
    } else if(response.statusCode == 401 || response.statusCode == 403) {
          Get.toNamed("/login");
    }
    return null;
  }

  Future<List<AdminOrder>?> getFinished() async {
    updateHeaders();
    final response = await get("/api/order/finished");
    if (response.isOk) {
      return (response.body as List)
          .map((o) => AdminOrder.fromJson(o as Map<String, dynamic>))
          .toList();
    }
    return null;
  }

  Future<bool> updateCost(String cost, int orderId) async {
    updateHeaders();
    var body = '{"cost":"$cost", "orderId":"$orderId" }';
    final response = await post("/api/order/update/cost", body, contentType: 'application/json');
    return response.isOk;
  }

  Future<bool> updateStatus(String status, int orderId) async {
    updateHeaders();
    var body = '{"status":"$status", "orderId":"$orderId" }';
    final response = await post("/api/order/update/status", body, contentType: 'application/json');
    return response.isOk;
  }

  Future<bool> deleteOrder(int orderId) async {
    updateHeaders();
    var body = '{"orderId":"$orderId" }';
    final response = await post("/api/order/delete", body, contentType: 'application/json');
    return response.isOk;
  }

  Future<List<AdminOrder>?> getArchive(String start, String end) async {
    updateHeaders();
    final response = await get("/api/order/search/$start/$end");
    if (response.isOk) {
      return (response.body as List)
          .map((o) => AdminOrder.fromJson(o as Map<String, dynamic>))
          .toList();
    }
    return null;
  }

}

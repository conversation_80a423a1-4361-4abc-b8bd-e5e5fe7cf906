import 'package:dostafka/app/modules/global/components/v2/password.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../global/components/btn_widget.dart';
import '../global/components/input_widget.dart';
import '../global/components/language_widget.dart';
import '../global/components/phone_widget.dart';
import '../global/constants.dart';
import 'registration_controller.dart';

class RegistrationView extends GetView<RegistrationController> {
  const RegistrationView({super.key});

  void toggle(bool? value) {
    FocusManager.instance.primaryFocus?.unfocus();
    controller.toggle();
  }

  void register() {
    FocusManager.instance.primaryFocus?.unfocus();
    controller.registerUser();
  }

  @override
  Widget build(BuildContext context) {
    controller.setContext(context);
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        actions: null,
        leading: null,
        elevation: 5,
        toolbarHeight: 200,
        automaticallyImplyLeading: false,
        backgroundColor: Globals.primeColor,
        title: const Image(
          image: AssetImage("assets/logo2.png"),
          width: 400,
          height: 200,
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                scrollDirection: Axis.vertical, //.horizontal
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      TextInput(
                        controller: controller.firstNameController,
                        label: LocaleKeys.common_firstName.tr,
                      ),
                      const SizedBox(height: 10),
                      PhoneInput(controller: controller.phoneController),
                      const SizedBox(height: 10),
                      // TextInput(
                      //   controller: controller.referenceController,
                      //   label: LocaleKeys.common_promocode.tr,
                      // ),
                      // const SizedBox(height: 10),
                      Password(
                        controller: controller.passwordController,
                        label: LocaleKeys.common_password.tr,
                      ),
                      const SizedBox(height: 10),
                      LanguageSelect(
                        languageController: controller.languageController,
                        showText: true,
                      ),
                      const SizedBox(height: 10),
                      Obx(() => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Checkbox(
                                value: controller.agreed.value,
                                onChanged: toggle,
                              ),
                              SizedBox(
                                width: 290,
                                child: TextButton(
                                    onPressed: () => Get.toNamed("/terms"),
                                    child: Text(
                                      LocaleKeys.termAndConditions_agree.tr,
                                      softWrap: true,
                                      overflow: TextOverflow.clip,
                                      style: const TextStyle(
                                        fontSize: 16,
                                      ),
                                    )),
                              ),

                            ],
                          )),
                      const SizedBox(height: 20),
                      Obx(() => controller.isInProgress.value
                          ? const CircularProgressIndicator(
                              color: Colors.greenAccent)
                          : Btn(
                              onPressFunction: controller.readyToRegister.value
                                  ? register
                                  : null,
                              label: LocaleKeys.button_signUp.tr,
                            )),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      // backgroundColor: Globals.primeColor,
    );
  }
}

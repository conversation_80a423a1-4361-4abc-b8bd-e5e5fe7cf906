import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../global/constants.dart';
import '../global/model/login_request_model.dart';
import '../global/model/server_response.dart';

class RegistrationProvider extends GetConnect {
  final jsonContentType = "application/json";

  @override
  void onInit() {
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return LoginResponse.fromJson(map);
      if (map is List) {
        return map.map((item) => LoginResponse.fromJson(item)).toList();
      }
    };
    httpClient.baseUrl = Globals.baseUrl;
  }

  Future<ServerResponse<LoginResponse?>> register(deviceId, phone, password,
          firstName, lastName, referenceCode, language) async =>
      callRegister(deviceId, phone, password, firstName, lastName,
          referenceCode, language);

  Future<ServerResponse<LoginResponse?>> callRegister(deviceId, phone, password,
          firstName, lastName, referenceCode, language) async =>
      await post(
              '/public/register',
              {
                'deviceId': deviceId,
                'phone': phone,
                'firstName': firstName,
                'lastName': lastName,
                'referenceCode': referenceCode,
                'language': language,
                'password': password,
                'fcmToken': GetStorage().read("fcm_token")
              },
              contentType: jsonContentType)
          .then((value) {
        return ServerResponse(
            value.isOk ? value.body : null, value.statusCode ?? -1);
      });
}

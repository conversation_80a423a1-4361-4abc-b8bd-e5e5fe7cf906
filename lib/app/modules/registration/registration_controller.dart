import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../../modules/registration/registration_provider.dart';
import '../global/controllers/language_controller.dart';
import '../global/controllers/state_controller.dart';
import '../global/model/user_model.dart';
import '../global/utils/general_utils.dart';

class RegistrationController extends GetxController {
  final LanguageController languageController = Get.find<LanguageController>();
  final StateController stateController = Get.find<StateController>();
  final RegistrationProvider registrationProvider =
      Get.find<RegistrationProvider>();

  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();
  final referenceController = TextEditingController();

  final isInProgress = false.obs;
  final readyToRegister = false.obs;
  final agreed = false.obs;
  final context = Rxn<BuildContext>(null);

  void toggle() {
    agreed(!agreed.value);
    readyToRegister(checkIfReady());
  }

  @override
  void onReady() {
    firstNameController.addListener(() => readyToRegister(checkIfReady()));
    lastNameController.addListener(() => readyToRegister(checkIfReady()));
    phoneController.addListener(() => readyToRegister(checkIfReady()));
    passwordController.addListener(() => readyToRegister(checkIfReady()));
    super.onReady();
  }

  bool checkIfReady() {
    return firstNameController.value.text.isNotEmpty &&
        //lastNameController.value.text.isNotEmpty &&
        phoneController.value.text.isNotEmpty &&
        phoneController.value.text.length == 10 &&
        passwordController.value.text.isNotEmpty &&
        agreed.value;
  }

  void registerUser() async {
    isInProgress(true);
    registrationProvider
        .register(
            stateController.getDeviceId(),
            phoneController.value.text,
            passwordController.value.text,
            firstNameController.value.text,
            "",
            referenceController.value.text,
            languageController.getLocale?.languageCode)
        .then((value) {
      if (value.code > 0) {
        if (value.code == 200 && value.payload?.token != null) {
          stateController.setOffline(false);
          agreed(false);
          stateController.setUser(UserModel(
              type: value.payload?.userType ?? 'NONE',
              phone: phoneController.value.text,
              token: value.payload?.token,
              authorized: value.payload?.token != null,
              id: value.payload?.id ?? -1));
          phoneController.clear();
          passwordController.clear();
          firstNameController.clear();
          lastNameController.clear();
          referenceController.clear();
          if (value.payload?.userType == 'ADMIN'){
            Get.toNamed('/admin');
          } else {
            Get.toNamed('/home');
          }
        } else {
          if (value.code == 409 && context.value != null) {
            showEasyDialog(
                context.value!,
                LocaleKeys.message_userExists_header.tr,
                LocaleKeys.message_userExists_body.tr,
                150,
                350);
          } else {
            showSnackbar(LocaleKeys.message_invalidCredential_header.tr,
                LocaleKeys.message_invalidCredential_body.tr);
          }
        }
      } else {
        stateController.setOffline(true);
        showSnackbar(LocaleKeys.message_offline_header.tr,
            LocaleKeys.message_offline_body.tr);
      }
    }).whenComplete(() => isInProgress(false));
  }

  void setContext(BuildContext context) {
    this.context(context);
  }
}

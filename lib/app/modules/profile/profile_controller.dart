
import 'package:dostafka/app/modules/global/utils/general_utils.dart';
import 'package:dostafka/app/modules/login/login_request_provider.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/global/controllers/state_controller.dart';
import 'package:dostafka/app/modules/profile/profile_response_model.dart';
import 'package:dostafka/app/modules/profile/providers/profile_response_provider.dart';

import '../global/controllers/language_controller.dart';

class ProfileController extends GetxController {
  final provider = Get.find<ProfileResponseProvider>();
  final loginProvider = Get.find<LoginProvider>();
  final stateController = Get.find<StateController>();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final passwordController = TextEditingController();
  final emailController = TextEditingController();
  final LanguageController languageController = Get.find<LanguageController>();
  final profile = Rxn<ProfileResponse>(null);
  final passwordChanged = false.obs;
  final nameChanged = false.obs;
  final processing = false.obs;
  final deleteCheck = false.obs;

  @override
  void onReady() {
    super.onReady();
    setListeners();
    loadProfile();
  }

  void loadProfile() {
    provider.getProfile(stateController.clientId.value!)
        .then((res) {
          profile(res);
          firstNameController.text = res!.firstName!;
          lastNameController.text = res.lastName!;
        });
  }

  void setListeners() {
    passwordController.addListener(
            () => passwordChanged(passwordController.text !='')
    );
    firstNameController.addListener(
            () => nameChanged(
                firstNameController.text != '' ||
                lastNameController.text != ''
            )
    );
    lastNameController.addListener(
            () => nameChanged(
                firstNameController.text != '' ||
                lastNameController.text != ''
        )
    );
  }

  void savePassword(){
    processing(true);
    provider.updatePassword(passwordController.text)
        .then((value){
          if (value){
            showSnackbar(LocaleKeys.message_success_header.tr, LocaleKeys.message_success_body.tr);
          } else {
            showSnackbar(LocaleKeys.message_error_header.tr, LocaleKeys.message_error_body.tr);
          }
        })
        .whenComplete(() => processing(false));
  }

  void saveDemographic(){
    processing(true);
    provider.updateDemographic(firstNameController.text, lastNameController.text)
        .then((value){
          if (value){
            showSnackbar(LocaleKeys.message_success_header.tr, LocaleKeys.message_success_body.tr);
          } else {
            showSnackbar(LocaleKeys.message_error_header.tr, LocaleKeys.message_error_body.tr);
          }
        })
        .whenComplete(() => processing(false));
  }

  void toggleDelete() {
    deleteCheck(!deleteCheck.value);
  }

  void deleteAccount(){
    processing(true);
    provider.deleteAccount(stateController.clientId.value!)
        .then((value){
          if (value){
            var user = stateController.getUser();
            loginProvider.logout(stateController.getDeviceId(), user.phone);
            user.authorized = false;
            user.token = null;
            user.phone = null;
            Get.toNamed('/login');
          }
          else {
            showSnackbar(LocaleKeys.message_error_header.tr, LocaleKeys.message_error_body.tr);
          }
    })
        .whenComplete((){
          processing(false);
          deleteCheck(false);
        });
  }

}

import 'package:dostafka/app/modules/login/login_request_provider.dart';
import 'package:get/get.dart';

import 'package:dostafka/app/modules/global/controllers/language_controller.dart';
import 'package:dostafka/app/modules/global/controllers/state_controller.dart';
import 'package:dostafka/app/modules/profile/providers/profile_response_provider.dart';

import 'profile_controller.dart';

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<StateController>(StateController());
    Get.put<ProfileResponseProvider>(ProfileResponseProvider());
    Get.put<LanguageController>(LanguageController());
    Get.put<LoginProvider>(LoginProvider());
    Get.put<ProfileController>(ProfileController());
  }
}

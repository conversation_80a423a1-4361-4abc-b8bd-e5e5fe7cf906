import 'package:dostafka/app/modules/global/components/v2/password.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../global/components/btn_widget.dart';
import '../global/components/input_widget.dart';
import '../global/components/label_widget.dart';
import '../global/components/language_widget.dart';
import '../global/constants.dart';
import 'profile_controller.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  void toggle(bool? value) {
    FocusManager.instance.primaryFocus?.unfocus();
    controller.toggleDelete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor),
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.only(left: 10, right: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Label(
                  LocaleKeys.drawer_profile.tr,
                  size: 20,
                  bold: true,
                ),
              ),
              const SizedBox(height: 10),
              TextInput(
                controller: controller.firstNameController,
                label: LocaleKeys.common_firstName.tr,
              ),
              const SizedBox(height: 10),
              TextInput(
                controller: controller.lastNameController,
                label: LocaleKeys.common_lastName.tr,
              ),
              const SizedBox(height: 10),
              // City
              const SizedBox(height: 10),
              // Area
              const SizedBox(height: 10),
              // address
              const SizedBox(height: 10),
              LanguageSelect(
                languageController: controller.languageController,
                showText: true,
              ),
              const SizedBox(height: 10),
              Obx(() => controller.processing.value
                  ? const CircularProgressIndicator()
                  : Btn(
                      onPressFunction: controller.nameChanged.value
                          ? controller.saveDemographic
                          : null,
                      label: LocaleKeys.button_save.tr,
                    )
              ),
              const SizedBox(height: 25),
              const Divider(color: Colors.green, thickness: 2),
              Label(LocaleKeys.common_change_password.tr),
              Password(
                controller: controller.passwordController,
                label: LocaleKeys.common_password.tr,
              ),
              const SizedBox(height: 10),
              Obx(() => controller.processing.value
                  ? const CircularProgressIndicator()
                  : Btn(
                      onPressFunction: controller.passwordChanged.value
                          ? controller.savePassword
                          : null,
                      label: LocaleKeys.button_save.tr,
                    )
              ),
              const SizedBox(height: 25),
              const Divider(color: Colors.green, thickness: 2),
              Label(LocaleKeys.common_delete_account.tr),
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Obx(()=>Checkbox(
                      value: controller.deleteCheck.value,
                      onChanged: toggle
                    ),
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width-80,
                    child: Text(LocaleKeys.common_delete_account_knowledge.tr, overflow: TextOverflow.ellipsis, maxLines: 6,),
                  )

                ],
              ),
              Obx(
                  ()=> controller.processing.value
                      ? const CircularProgressIndicator()
                      : Btn(
                    onPressFunction: controller.deleteCheck.value
                        ? controller.deleteAccount
                        : null,
                    label: LocaleKeys.button_delete.tr,
                  )
              )

            ],
          ),
        ),
      ),
    );
  }
}


import 'package:dostafka/app/modules/global/basic_api.dart';

import '../profile_response_model.dart';

class ProfileResponseProvider extends BasicAPI {
  Future<ProfileResponse?> getProfile(int id) async {
    updateHeaders();
    final response = await get('/api/profile/${stateController.clientId}');
    if (response.statusCode != null && response.statusCode == 200) {
      var profile = ProfileResponse.fromJson(response.body);
      return profile;
    }
    return null;
  }

  Future<bool> postProfile(ProfileResponse profileresponse) async {
    updateHeaders();
    final response =
        await post('/api/profile/${stateController.clientId}', profileresponse);
    return response.isOk;
  }

  Future<bool> updatePassword(String password) async {
    updateHeaders();
    final response =
        await post(
            '/api/profile/password',
            PasswordUpdateRequest(stateController.clientId.value, password).toJson()
        );
    return response.isOk;
  }

  Future<bool> updateDemographic(String firstName, String lastName) async {
    updateHeaders();
    final response =
        await post(
            '/api/profile/demographic',
            DemographicUpdateRequest(stateController.clientId.value, firstName, lastName).toJson()
        );
    return response.isOk;
  }

  Future<bool> deleteAccount(int id) async {
    updateHeaders();
    final response =
        await delete('/api/profile/$id');
    return response.isOk;
  }

}

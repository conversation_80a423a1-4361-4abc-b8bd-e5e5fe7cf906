class ProfileResponse {
  int? id;
  int? phone;
  String? firstName;
  String? lastName;
  String? language;
  String? address;
  String? city;

  ProfileResponse(
      {this.id,
      this.phone,
      this.firstName,
      this.lastName,
      this.language,
      this.address,
      this.city});

  ProfileResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    phone = json['phone'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    language = json['language'];
    address = json['address'];
    city = json['city'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['phone'] = phone;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['language'] = language;
    data['address'] = address;
    data['city'] = city;
    return data;
  }
}

class PasswordUpdateRequest {
  int? id;
  String? password;

  PasswordUpdateRequest(this.id, this.password);

  PasswordUpdateRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    password = json['password'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['password'] = password;
    return data;
  }
}

class DemographicUpdateRequest {
  int? id;
  String? firstName;
  String? lastName;

  DemographicUpdateRequest(this.id, this.firstName, this.lastName);

  DemographicUpdateRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['firstName'];
    lastName = json['lastName'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    return data;
  }

}
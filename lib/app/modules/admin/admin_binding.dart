import 'package:dostafka/app/modules/login/login_request_provider.dart';
import 'package:get/get.dart';

import '../home/<USER>/order_provider.dart';
import 'admin_controller.dart';

class AdminBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AdminController>(
      () => AdminController(),
    );
    Get.lazyPut<LoginProvider>(
      ()=> LoginProvider()
    );
    Get.lazyPut<OrderProvider>(
      ()=> OrderProvider()
    );

  }
}

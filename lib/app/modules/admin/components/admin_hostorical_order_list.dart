
import 'dart:developer';

import 'package:dostafka/app/modules/admin/admin_controller.dart';
import 'package:dostafka/app/modules/global/components/label_widget.dart';
import 'package:dostafka/app/modules/global/constants.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../generated/locales.g.dart';
import '../../global/components/button_widget.dart';
import '../../global/model/order_model.dart';
import 'orders/admin_order_card.dart';

class AdminHistoricalOrderList extends StatelessWidget {

  final AdminController controller;

  final startDate = DateFormat("dd-MM-yyyy").format(DateTime.now()).obs;
  final endDate = DateFormat("dd-MM-yyyy").format(DateTime.now()).obs;

  AdminHistoricalOrderList(this.controller, {super.key});

  Future<void> _showStartDatePicker(BuildContext context, RxString date) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime.now().subtract(const Duration(days: 365)),
        lastDate: DateTime.now(),
        fieldLabelText: LocaleKeys.common_date.tr,
        cancelText: LocaleKeys.button_cancel.tr,
        confirmText: LocaleKeys.button_ok.tr,
        helpText: LocaleKeys.common_date.tr,
        errorFormatText: LocaleKeys.message_error_header.tr,
        errorInvalidText: LocaleKeys.message_error_header.tr,
    );
    if (picked != null) {
      date(DateFormat("dd-MM-yyyy").format(picked));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Label(
            LocaleKeys.common_archive.tr,
            size: 20,
            bold: true
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(LocaleKeys.common_dateFrom.tr),
            Obx(() =>
              Button(
                onPressFunction: () => _showStartDatePicker(context, startDate),
                label: startDate.value,
                size: 118,
                fontsize: 12
              )
            ),
            Text(LocaleKeys.common_dateTo.tr),
            Obx(() =>
              Button(
                onPressFunction: () => _showStartDatePicker(context, endDate),
                label: endDate.value,
                size: 118,
                fontsize: 12,
              )
            ),
            searchButton()
          ],
        ),
        const SizedBox(height: 10,),
        Expanded(
            child: VisibilityDetector(
              key: const Key('adminArchivedOrderList'),
              onVisibilityChanged: (VisibilityInfo info){
                log("adminArchivedOrderList visibility changed");
                if (controller.flushed.value){
                  controller.flushed(false);
                }
              },
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                padding: const EdgeInsets.only(left: 10, right: 10, top: 5),
                physics: const AlwaysScrollableScrollPhysics(),
                controller: ScrollController(),
                child: Obx(() =>
                controller.isLoading.value
                    ? const SizedBox( height: 35, width: 35, child: CircularProgressIndicator())
                    : Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const SizedBox(height: 15,),
                    ...showOrders(context)
                  ],
                )
                ),
              ),
            )

        )
      ],
    );
  }

  SizedBox searchButton() {
    return SizedBox(
            height: 45,
            width: 45,
            child: Card(
              surfaceTintColor: Colors.white,
              elevation: 5,
              child: IconButton(
                onPressed: () => controller.isLoading.value
                    ? null
                    : controller.loadArchived(startDate.value, endDate.value)
                      .whenComplete(() => controller.flushed(true)),
                icon: const Icon(Icons.search),
                color: Globals.primeColor,
              ),
            ),
          );
  }

  List<Widget> showOrders(context){
    List <Widget> result = [];
    log("Size: ${controller.historicalOrders.value.length}");
    if (controller.historicalOrders.value.isNotEmpty){
      final orders =  controller.historicalOrders.value;
      final Map<String, List<AdminOrder>> orderMap = {};
      for (var order in orders) {
        List<AdminOrder>? list = orderMap.containsKey(order.cityArea)
            ? orderMap[order.cityArea]
            : [];
        list?.add(order);
        orderMap[order.cityArea] = list!;
      }
      for (var key in orderMap.keys) {
        Widget cityCard = Card(
          elevation: 2,
          surfaceTintColor: Colors.white,
          child: ExpandablePanel(
            header: Container(
              padding: const EdgeInsets.only(left: 5, top: 4),
              child: Text(key, style: const TextStyle(fontSize: 18),),
            ),
            theme: Globals.expandableThemeData,
            controller: ExpandableController(initialExpanded: orderMap[key]
                ?.any((o) => o.orderId == controller.lastViewed.value)),
            collapsed: Text("  ${LocaleKeys.order_total.tr}: ${orderMap[key]?.length}"),
            expanded: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: orderMap[key]
              !.map((o) => AdminOrderCard(controller, o, true))
                  .toList(),
            ),
          ),
        );
        result.add(cityCard);
      }
    }
    return result.isNotEmpty
        ? result
        : [ Text(LocaleKeys.order_empty.tr) ];
  }

}
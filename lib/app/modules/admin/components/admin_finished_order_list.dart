
import 'package:dostafka/app/modules/admin/admin_controller.dart';
import 'package:dostafka/app/modules/admin/components/orders/admin_closed_order_card.dart';
import 'package:dostafka/app/modules/global/model/order_model.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../global/components/label_widget.dart';
import '../../global/constants.dart';

class AdminFinishedOrderList extends StatelessWidget {

  final AdminController controller;

  final filterController = ExpandableController(initialExpanded: false);

  AdminFinishedOrderList(this.controller, {super.key});

  final expandableThemeData = const ExpandableThemeData(
      tapBodyToExpand: true,
      tapHeaderToExpand: true,
      hasIcon: true,
      collapseIcon: Icons.arrow_drop_down_sharp,
      expandIcon: Icons.arrow_drop_up_sharp,
      iconColor: Globals.primeColor
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        pageHeader(),
        Expanded(
          child: RefreshIndicator(
            triggerMode: RefreshIndicatorTriggerMode.anywhere,
            onRefresh: controller.loadFinishedOrders,
            color: Globals.primeColor,
            child: getMainList(context),
          ),
        ),
      ],
    );
  }

  SingleChildScrollView getMainList(context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      padding: const EdgeInsets.only(left: 10, right: 10, top: 5),
      physics: const AlwaysScrollableScrollPhysics(),
      controller: ScrollController(),
      child: Obx(() =>
      controller.isLoading.value
          ? const SizedBox( height: 35, width: 35, child: CircularProgressIndicator())
          : Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 15,),
          ...showFinishedOrders(context)
        ],
      )
      ),
    );
  }

  Row pageHeader() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const SizedBox(height: 10),
        Label(LocaleKeys.order_finished.tr, size: 20, bold: true),
        const SizedBox(height: 10),
      ],
    );
  }

  List<Widget> showFinishedOrders(context) {
    List <Widget> result = [];
    if (controller.finishedOrders.value.isNotEmpty){
      final orders = controller.finishedOrders.value
          .where((o) => o.status=='EXECUTED')
          .toList();
      final Map<String, List<AdminOrder>> orderMap = {};
      for (var order in orders) {
        List<AdminOrder>? list = orderMap.containsKey(order.cityArea)
            ? orderMap[order.cityArea]
            : [];
        list?.add(order);
        orderMap[order.cityArea] = list!;
      }
      for (var key in orderMap.keys) {
        Widget cityCard = Card(
          elevation: 2,
          surfaceTintColor: Colors.white,
          child: ExpandablePanel(
            header: Container(
              padding: const EdgeInsets.only(left: 5, top: 4),
              child: Text(key, style: const TextStyle(fontSize: 18),),
            ),
            theme: expandableThemeData,
            controller: ExpandableController(initialExpanded: controller.expandAllOrders.value || orderMap[key]
                ?.any((o) => o.orderId == controller.lastViewed.value)),
            collapsed: Text("  ${LocaleKeys.order_total.tr}: ${orderMap[key]?.length}"),
            expanded: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: orderMap[key]
              !.map((o) => AdminClosedOrderCard(controller, o))
                  .toList(),
            ),
          ),
        );
        result.add(cityCard);
      }
    }
    return result.isNotEmpty ? result : [ Text(LocaleKeys.order_empty.tr) ];
  }

}

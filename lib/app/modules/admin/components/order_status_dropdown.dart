import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrderStatusSelect extends StatelessWidget {
  final RxString selectedValue;
  final Function(String) onPress;

  OrderStatusSelect(String currentValue, this.onPress, {super.key})
      : selectedValue = RxString(currentValue);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return DropdownButton<String>(
        onChanged: (v) {
          selectedValue.value = v!;
          onPress(v);
        },
        value: selectedValue.value,
        items: [
          DropdownMenuItem(value: "WAITING", child: Text(LocaleKeys.WAITING.tr)),
          DropdownMenuItem(value: "PICKED", child: Text(LocaleKeys.PICKED.tr)),
          DropdownMenuItem(value: "COMING", child: Text(LocaleKeys.COMING.tr)),
          DropdownMenuItem(value: "EXECUTED", child: Text(LocaleKeys.EXECUTED.tr)),
        ],
      );
    });
  }
}



import 'package:dostafka/app/modules/admin/admin_controller.dart';
import 'package:dostafka/app/modules/global/constants.dart';
import 'package:dostafka/app/modules/global/model/order_model.dart';
import 'package:dostafka/app/v2/components/order/d_card.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../generated/locales.g.dart';
import '../../global/components/label_widget.dart';
import 'filters/order_filters.dart';

class AdminActiveOrderList extends StatelessWidget {

  final AdminController controller;

  final filterController = ExpandableController(initialExpanded: false);

  AdminActiveOrderList(this.controller, {super.key});

  final double filterBlockSize = 85;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Obx(() => getHeader()),
        Expanded(
          child: RefreshIndicator(
            triggerMode: RefreshIndicatorTriggerMode.anywhere,
            onRefresh: controller.loadActiveOrders,
            color: Globals.primeColor,
            child: VisibilityDetector(
              key: const Key('adminActiveOrderList'),
              onVisibilityChanged: (VisibilityInfo info){
                if (controller.flushed.value){
                  controller.flushed(false);
                }
              },
              child: getMainList(context),
            ),
          ),
        ),
      ],
    );
  }

  SingleChildScrollView getMainList(context) {
    return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              padding: const EdgeInsets.only(left: 10, right: 10, top: 5),
              physics: const AlwaysScrollableScrollPhysics(),
              controller: ScrollController(),
              child: Obx(() =>
                controller.isLoading.value
                ? const SizedBox( height: 35, width: 35, child: CircularProgressIndicator())
                : Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10,),
                    ...showActiveOrders(context)
                  ],
                )
              ),
            );
  }

  ExpandablePanel getHeader() {
    return ExpandablePanel(
        header: pageHeader(),
        controller: filterController,
        key: const ValueKey("filters"),
        theme: getFilterTheme(),
        collapsed: const SizedBox(),
        expanded: OrderFilters(
            filterBlockSize: filterBlockSize,
            controller: controller
          ),
        );
  }

  ExpandableThemeData getFilterTheme() {
    return ExpandableThemeData(
          hasIcon: true,
          iconColor: [
              controller.showWaiting,
              controller.showPicked,
              controller.showComing,
              controller.showAdjusted,
              controller.showNotAdjusted,
              controller.showSending,
            controller.showDelivery]
              .any((e) => e.value==false)
                  ? Colors.redAccent
                  : Globals.primeColor,
          iconSize: 18,
          iconPadding: const EdgeInsets.all(4),
          expandIcon: Icons.filter_list_alt,
          collapseIcon: Icons.filter_list_alt,
          tapBodyToExpand: false,
          tapHeaderToExpand: false,
          iconRotationAngle: 0
        );
  }

  Row pageHeader() {
    return Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(height: 10,),
            Label(LocaleKeys.order_active.tr, size: 20, bold: true),
            const SizedBox(),
          ],
        );
  }


  List<Widget> showActiveOrders(context) {
    List <Widget> result = [];
    if (controller.activeOrders.value.isNotEmpty){
      final orders = !controller.flushed.value
          ? controller.activeOrders.value
                .where((o) => getAppliedFilter(o))
                .where((o) => o.status!='EXECUTED')
                .toList()
          : [];
      final Map<String, List<AdminOrder>> orderMap = {};
      for (var order in orders) {
        List<AdminOrder>? list = orderMap.containsKey(order.cityArea)
            ? orderMap[order.cityArea]
            : [];
        list?.add(order);
        orderMap[order.cityArea] = list!;
      }
      for (var key in orderMap.keys) {
        Widget cityCard = Card(
          elevation: 2,
          surfaceTintColor: Colors.white70,
          child: ExpandablePanel(
            header: Container(
              color: Colors.white,
              padding: const EdgeInsets.only(left: 5, top: 4),
              child: Text(key, style: const TextStyle(fontSize: 18),),
            ),
            theme: Globals.expandableThemeData,
            controller: ExpandableController(initialExpanded: orderMap[key]
                ?.any((o) => o.orderId == controller.lastViewed.value)),
            collapsed: Text("  ${LocaleKeys.order_total.tr}: ${orderMap[key]?.length}"),
            expanded: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: orderMap[key]
                !.map((o) => DCard(o, controller)) // AdminOrderCard(controller, o, false))
                .toList(),
            ),
          ),
        );
        result.add(cityCard);
      }
    }
    return result.isNotEmpty ? result : [ Text(LocaleKeys.order_empty.tr) ];
  }


  bool getAppliedFilter(AdminOrder o) {
    return (controller.showDelivery.value || o.type != 'DELIVERY') &&
        (controller.showSending.value || o.type != 'SENDING') &&
        (controller.showWaiting.value || o.status != 'WAITING') &&
        (controller.showPicked.value || o.status != 'PICKED') &&
        (controller.showComing.value || o.status != 'COMING') &&
        (controller.showAdjusted.value || !o.adjusted) &&
        (controller.showNotAdjusted.value || o.adjusted);
  }

}

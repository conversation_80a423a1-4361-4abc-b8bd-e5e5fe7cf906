
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class ReceivingFilter extends StatelessWidget {
  const ReceivingFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Obx(() => controller.showDelivery.value
            ? IconButton(
          icon: const Icon(Icons.home, color: Colors.green,),
          onPressed: () => controller.showDelivery(false),
        )
            : IconButton(
          icon: const Icon(Icons.home, color: Colors.grey,),
          onPressed: () => controller.showDelivery(true),
        )
        ),
        Text(LocaleKeys.order_type_delivery.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}
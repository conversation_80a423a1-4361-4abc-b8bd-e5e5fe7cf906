
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class PickedFilter extends StatelessWidget {
  const PickedFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Obx(() => controller.showPicked.value
            ? IconButton(
          icon: const Icon(Icons.alarm_rounded, color: Colors.yellow),
          onPressed: () => controller.showPicked(false),
        )
            : IconButton(
          icon: const Icon(Icons.alarm_off, color: Colors.yellow),
          onPressed: () => controller.showPicked(true),
        )
        ),
        Text(LocaleKeys.COMING.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}
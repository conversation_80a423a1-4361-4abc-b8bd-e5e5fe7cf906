
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class AdjustedCostFilter extends StatelessWidget {
  const AdjustedCostFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Obx(() => controller.showNotAdjusted.value
            ? IconButton(
          icon: const Icon(Icons.warning_amber, color: Colors.yellow,),
          onPressed: () => controller.showNotAdjusted(false),
        )
            : IconButton(
          icon: const Icon(Icons.warning_amber, color: Colors.grey,),
          onPressed: () => controller.showNotAdjusted(true),
        )
        ),
        Text(LocaleKeys.order_cost_filter_not_approved.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}
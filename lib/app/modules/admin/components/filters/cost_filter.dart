
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';
import 'adjusted_cost_filter.dart';
import 'all_cost_button.dart';
import 'not_adjusted_cost_filter.dart';

class CostFilter extends StatelessWidget {
  const CostFilter({
    super.key,
    required this.filterBlockSize,
    required this.controller,
  });

  final double filterBlockSize;
  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return
      Padding(
        padding: const EdgeInsets.only(left: 5, right: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: filterBlockSize,
              child: Text("${LocaleKeys.order_cost_title.tr}:"),
            ),
            SizedBox(
              width: 240,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: 82,
                    child: AdjustedCost<PERSON>ilter(controller: controller),
                  ),
                  SizedBox(
                    width: 82,
                    child: NotAdjustedCostFilter(controller: controller),
                  ),
                ],
              ),
            ),
            AllCostButton(controller: controller)
          ],
        ),
      );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class SendingFilter extends StatelessWidget {
  const SendingFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Obx(() => controller.showSending.value
            ? IconButton(
          icon: const Icon(Icons.fire_truck_sharp, color: Colors.green,),
          onPressed: () => controller.showSending(false),
        )
            : IconButton(
          icon: const Icon(Icons.fire_truck_sharp, color: Colors.grey,),
          onPressed: () => controller.showSending(true),
        )
        ),
        Text(LocaleKeys.order_type_sending.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}
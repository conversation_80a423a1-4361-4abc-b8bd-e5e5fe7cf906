
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class NotAdjustedCostFilter extends StatelessWidget {
  const NotAdjustedCostFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Obx(() => controller.showAdjusted.value
            ? IconButton(
          icon: const Icon(Icons.check, color: Colors.green,),
          onPressed: () => controller.showAdjusted(false),
        )
            : IconButton(
          icon: const Icon(Icons.check, color: Colors.grey,),
          onPressed: () => controller.showAdjusted(true),
        )
        ),
        Text(LocaleKeys.order_cost_filter_approved.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}

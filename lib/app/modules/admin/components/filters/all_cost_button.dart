
import 'package:flutter/material.dart';

import '../../admin_controller.dart';

class AllCostButton extends StatelessWidget {
  const AllCostButton({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.check_box_outlined, color: Colors.blue),
      onPressed: (){
        controller.showAdjusted(true);
        controller.showNotAdjusted(true);
      },
    );
  }
}
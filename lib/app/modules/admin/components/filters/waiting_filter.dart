
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class WaitingFilter extends StatelessWidget {
  const WaitingFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      textBaseline: null,
      children: [
        Obx(() => controller.showWaiting.value
            ? IconButton(
          icon: const Icon(Icons.alarm_rounded, color: Colors.red),
          onPressed: () => controller.showWaiting(false),
        )
            : IconButton(
          icon: const Icon(Icons.alarm_off_rounded, color: Colors.red),
          onPressed: () => controller.showWaiting(true),
        )
        ),
        Text(LocaleKeys.WAITING.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}



import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';
import 'all_statuses.dart';
import 'coming_filter.dart';
import 'picked_filter.dart';
import 'waiting_filter.dart';

class StatusFilter extends StatelessWidget {
  const StatusFilter({
    super.key,
    required this.filterBlockSize,
    required this.controller,
  });

  final double filterBlockSize;
  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return
      Padding(
        padding: const EdgeInsets.only(left: 5, right: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Sized<PERSON><PERSON>(width: filterBlockSize, child: Text("${LocaleKeys.order_status_title.tr}: ")),
            SizedBox(
              width: 240,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.max,
                children: [
                  <PERSON>zed<PERSON>ox(
                    width: 42,
                    child: Waiting<PERSON><PERSON>er(controller: controller),
                  ),
                  <PERSON><PERSON><PERSON><PERSON>(
                    width: 42,
                    child: <PERSON><PERSON><PERSON><PERSON><PERSON>(controller: controller),
                  ),
                  <PERSON><PERSON><PERSON><PERSON>(
                    width: 42,
                    child: ComingFilter(controller: controller),
                  ),
                ],
              ),
            ),
            AllStatuses(controller: controller),
          ],
        ),
      );
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';

class ComingFilter extends StatelessWidget {
  const ComingFilter({
    super.key,
    required this.controller,
  });

  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Obx(() => controller.showComing.value
            ? IconButton(
          icon: const Icon(Icons.alarm_rounded, color: Colors.lime),
          onPressed: () => controller.showComing(false),
        )
            : IconButton(
          icon: const Icon(Icons.alarm_off, color: Colors.lime),
          onPressed: () => controller.showComing(true),
        )
        ),
        Text(LocaleKeys.PICKED.tr, style: const TextStyle(fontSize: 8),),
      ],
    );
  }
}
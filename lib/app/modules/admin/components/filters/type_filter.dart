
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../admin_controller.dart';
import 'all_service_button.dart';
import 'receiving_filter.dart';
import 'sending_filter.dart';

class TypeFilter extends StatelessWidget {
  const TypeFilter({
    super.key,
    required this.filterBlockSize,
    required this.controller,
  });

  final double filterBlockSize;
  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return
      Padding(
        padding: const EdgeInsets.only(left: 5, right: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedB<PERSON>(
                width: filterBlockSize,
                child: Text("${LocaleKeys.order_type_type.tr}:")
            ),
            Si<PERSON><PERSON><PERSON>(
              width: 240,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.max,
                children: [
                  <PERSON><PERSON><PERSON><PERSON>(
                    width: 82,
                    child: Receiving<PERSON>ilter(controller: controller),
                  ),
                  <PERSON><PERSON><PERSON><PERSON>(
                    width: 82,
                    child: SendingFilter(controller: controller),
                  )
                ],
              ),
            ),
            AllService<PERSON><PERSON>on(controller: controller)
          ],
        ),
      );
  }
}
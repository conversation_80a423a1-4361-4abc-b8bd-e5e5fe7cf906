
import 'package:flutter/material.dart';

import '../../admin_controller.dart';
import 'cost_filter.dart';
import 'status_filter.dart';
import 'type_filter.dart';

class OrderFilters extends StatelessWidget {
  const OrderFilters({
    super.key,
    required this.filterBlockSize,
    required this.controller,
  });

  final double filterBlockSize;
  final AdminController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StatusFilter(filterBlockSize: filterBlockSize, controller: controller),
        CostFilter(filterBlockSize: filterBlockSize, controller: controller),
        TypeFilter(filterBlockSize: filterBlockSize, controller: controller),
      ],
    );
  }
}
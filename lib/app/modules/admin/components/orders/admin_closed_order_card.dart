

import 'dart:developer';

import 'package:dostafka/app/modules/admin/admin_controller.dart';
import 'package:dostafka/app/modules/global/model/order_model.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:image_loader/image_helper.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../generated/locales.g.dart';
import '../../../global/components/btn_widget.dart';
import '../../../global/components/labled_text.dart';
import '../../../global/constants.dart';
import '../../../global/utils/general_utils.dart';

class AdminClosedOrderCard extends StatelessWidget {

  final AdminController controller;
  final AdminOrder o;
  late RxString status;
  late RxInt cost;
  late RxBool adjusted;
  late ExpandableController expandableController;

  AdminClosedOrderCard(this.controller, this.o, {super.key}){
    status = o.status.obs;
    cost = o.cost.obs;
    adjusted = o.adjusted.obs;
    expandableController = ExpandableController(initialExpanded: o.orderId == controller.lastViewed.value);
  }

  String nextStatus(){
    switch (status.value){
      case "WAITING": return LocaleKeys.COMING;
      case "PICKED" : return LocaleKeys.COMING;
      case "COMING" : return LocaleKeys.EXECUTED;
      default: return "";
    }

  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Card(
        elevation: 4,
        color: Colors.white,
        surfaceTintColor: Colors.white,
        //shadowColor: Globals.primeColor,
        child: ExpandablePanel(
          controller: expandableController,
          header: getHeaderCard(context),
          collapsed: const SizedBox(),
          expanded: getExtendedCard(context),
          theme: const ExpandableThemeData(
              hasIcon: false
          ),
        ),
      ),
    );
  }

  Column getExtendedCard(BuildContext context) {
    return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(4),
              child: LabeledText(o.type.tr, LocaleKeys.order_type_type.tr),
            ),
            Padding(
              padding: const EdgeInsets.all(4),
              child: LabeledText(o.status.tr, LocaleKeys.order_status_title.tr),
            ),
            Padding(
                padding: const EdgeInsets.all(4),
                child: o.comment!=''
                    ? LabeledText(o.comment, LocaleKeys.order_comment.tr)
                    : const SizedBox(width: 0, height: 0)
            ),
            Padding(
              padding: const EdgeInsets.all(4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: o.imagesId
                    .map((imageId) => ImageHelper(
                        imageShape: ImageShape.rectangle,
                        borderRadius: const BorderRadius.all(
                            Radius.circular(20.0)
                        ),
                        width: MediaQuery.of(context).size.width,
                        boxFit: BoxFit.fitWidth,
                        image: "${Globals.baseUrl}/public/api/image/$imageId",
                        imageType: ImageType.network,
                        defaultLoaderColor: Globals.primeColor,
                        defaultErrorBuilderColor: Colors.blueGrey,
                        errorBuilder: _errorBuilderIcon,
                      )
                    )
                    .toList(),
              ),
            ),
            SizedBox(
                width: double.infinity,
                child: TextButton(
                    onPressed: expandableController.toggle,
                    child: Text(LocaleKeys.button_close.tr)
                )
            )
          ],
        );
  }

  Row getHeaderCard(context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            SizedBox(
                width: 8,
                height: 35,
                child: Obx(() => Container(
                  margin: const EdgeInsets.only(top: 4, left: 2, right: 2, bottom: 2),
                  color: _statusToColor(status.value),
                ))
            ),
            SizedBox(
                width: 8,
                height: 35,
                child: Obx(() => Container(
                  margin: const EdgeInsets.only(top: 4, left: 2, right: 2, bottom: 2),
                  color: _costToColor(adjusted.value),
                ))
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
                width: 60,
                height: 40,
                child: LabeledText("${o.orderId}", LocaleKeys.order_no.tr)
            ),
            Obx(() => LabeledText("${cost.value}", LocaleKeys.order_cost_title.tr)),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
                width: 70,
                height: 40,
                child: LabeledText(o.orderDate, LocaleKeys.common_date.tr)
            ),
            LabeledText(o.phone, LocaleKeys.common_phone.tr)
          ],
        ),
        SizedBox(
            width: 90,
            child: LabeledText(o.name, LocaleKeys.common_name.tr)
        ),
        SizedBox(
            width: MediaQuery.of(context).size.width-90-70-60-8-45,
            child: LabeledText(o.address, LocaleKeys.common_address.tr)
        ),
      ],
    );
  }

  ActionPane _startActions() {
    return ActionPane(
            motion: const BehindMotion(),
            extentRatio: 0.2,
            children: [
              _deleteAction()
            ],
          );
  }

  ActionPane _endActions() {
    return ActionPane(
            motion: const BehindMotion(),
            extentRatio: 0.5,
            children: [
              _editAction(),
              _updatePriceAction(),
              _phoneCallAction(),
            ],
          );
  }

  SlidableAction _deleteAction() {
    return SlidableAction(
      onPressed: status.value != 'EXECUTED'
          ? (ctx) => {
                controller.delete(o.orderId)
                .whenComplete(() => controller.loadActiveOrders())}
          : null,
      borderRadius: BorderRadius.circular(4),
      autoClose: true,
      spacing: 10,
      icon: status.value == 'WAITING' ? Icons.delete : Icons.delete_forever_outlined,
      foregroundColor: status.value == 'WAITING' ? Colors.white : Colors.white54,
      backgroundColor: status.value == 'WAITING' ? Colors.red : Colors.grey,
    );
  }

  SlidableAction _editAction() {
    return SlidableAction(
      icon: status.value != 'EXECUTED' ? Icons.edit: Icons.edit_off,
      foregroundColor: status.value != 'EXECUTED' ? Colors.white : Colors.white54,
      backgroundColor: status.value != 'EXECUTED' ? Colors.orange : Colors.grey,
      autoClose: true,
      spacing: 5,
      borderRadius: BorderRadius.circular(4),
      onPressed: status.value != 'EXECUTED' ? _updateStatus : null,
    );
  }

  void _updateStatus(ctx) {
      showWidgetDialog(ctx,
          LocaleKeys.order_status_title.tr,
          250,
          MediaQuery.of(ctx).size.width-80,
          [
            const SizedBox(height: 25),
            Obx(() =>
              Btn(
                onPressFunction: (){
                  var expectedStatus = nextStatus();
                  controller.orderProvider.updateStatus(expectedStatus, o.orderId)
                      .whenComplete((){
                        final orders = controller.activeOrders.value;
                        try {
                          orders
                              .firstWhere((element) => element.orderId == o.orderId)
                              .status = expectedStatus;
                        } on Exception catch (e) {
                          log("error: ${e.toString()}");
                        }
                        controller.activeOrders(orders);
                        controller.flushed(true);
                        Get.close(0);
                  });
                },
                label: nextStatus().tr
              )
            ),
            const SizedBox(height: 25),
            TextButton(
              child: Text(LocaleKeys.button_close.tr),
              onPressed: () {
                Get.close(0);
              },
            )
          ]
      );
    }

  SlidableAction _phoneCallAction() {
    return SlidableAction(
                icon: Icons.call,
                backgroundColor: Colors.green,
                autoClose: true,
                spacing: 10,
                borderRadius: BorderRadius.circular(4),
                onPressed: (ctx) => _makingPhoneCall(o.phone),
              );
  }

  SlidableAction _updatePriceAction() {
    return SlidableAction(
                icon: Icons.attach_money_sharp,
                backgroundColor: Colors.blue,
                autoClose: true,
                spacing: 5,
                borderRadius: BorderRadius.circular(4),
                onPressed: _updatePrice,
              );
  }

  _makingPhoneCall(phone) async {
    var url = Uri.parse("tel:+7$phone");
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  Widget get _errorBuilderIcon => const Icon(
    Icons.image_not_supported,
    size: 10000,
  );

  ColorSwatch<int> _statusToColor(String status) {
    switch (status) {
      case 'COMING':
        return Colors.yellow;
      case 'PICKED':
        return Colors.lime;
      case 'EXECUTED':
        return Colors.green;
      default:
        return Colors.red;
    }
  }

  ColorSwatch<int> _costToColor(bool adjusted) {
    return adjusted
        ? Colors.green
        : Colors.red;
  }

  _updatePrice(ctx){
    final costController = TextEditingController(text: "${cost.value}");
    controller.lastViewed(o.orderId);
    showWidgetDialog(
      ctx,
      LocaleKeys.order_cost_adjust_title.tr,
      200,
      MediaQuery.of(ctx).size.width-80,
      [
        Text(LocaleKeys.order_cost_adjust_body.tr),
        TextField(
          keyboardType: TextInputType.number,
          controller: costController,
        ),
        Btn(
          label: LocaleKeys.button_save.tr,
          onPressFunction: () {
            cost(int.parse(costController.text));
            controller.orderProvider
              .updateCost(costController.text, o.orderId)
              .whenComplete((){
                final orders = controller.activeOrders.value;
                try {
                  orders
                    .firstWhere((element) => element.orderId == o.orderId)
                    .cost = int.parse(costController.text);
                } on Exception catch (e) {
                  log("error: ${e.toString()}");
                }
                controller.activeOrders(orders);
                controller.flushed(true);
                Get.close(0);
              }
            );
          },
        )
      ]
    );
  }
}
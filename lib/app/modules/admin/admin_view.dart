import 'package:dostafka/app/modules/adminNotification/admin_notification_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:animated_floating_buttons/animated_floating_buttons.dart';
import '../global/constants.dart';
import '../home/<USER>/bottom_bars.dart';
import '../global/components/drawer.dart';
import 'admin_controller.dart';
import 'components/admin_active_order_list.dart';
import 'components/admin_finished_order_list.dart';
import 'components/admin_hostorical_order_list.dart';

class AdminView extends GetView<AdminController>{

  final GlobalKey<AnimatedFloatingActionButtonState> keyset = GlobalKey<AnimatedFloatingActionButtonState>();

  AdminView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: true,
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor
      ),
      drawer: AdminDrawer(controller),
      body: WillPopScope (
        onWillPop: (){
          SystemNavigator.pop(animated: true);
          return Future.value(false);
        },
        child: SafeArea(
          maintainBottomViewPadding: true,
          child: Container(
            alignment: Alignment.topCenter,
            child: Obx((){
              switch (controller.view.value){
                case 'ORDER_TODAY_NEW': return AdminActiveOrderList(controller);
                case 'ORDER_TODAY_DONE': return AdminFinishedOrderList(controller);
                case 'ORDER_HISTORY': return AdminHistoricalOrderList(controller);
                case 'NOTIFICATION': return const AdminNotificationView();
                default: return AdminActiveOrderList(controller);
              }
            }),
          ),
        ),
      ),
      floatingActionButton: AnimatedFloatingActionButton(
        key: keyset,
        colorStartAnimation: Globals.primeColor,
        colorEndAnimation: Colors.blue,
        animatedIconData: AnimatedIcons.menu_close,
        curve: Curves.linear,
        fabButtons: [
          FloatingActionButton(
            heroTag: "add_order",
            backgroundColor: Globals.primeColor,
            elevation: 6,
            shape: const CircleBorder(),
            onPressed: () => Get.toNamed("/admin-add-order"),
            child: const Icon(Icons.task),
          ),
          FloatingActionButton(
            heroTag: "add_notification",
            backgroundColor: Globals.primeColor,
            elevation: 6,
            shape: const CircleBorder(),
            onPressed: () => Get.toNamed("/admin-notification"),
            child: const Icon(Icons.message_outlined),
          ),
          // FloatingActionButton(
          //   backgroundColor: Globals.primeColor,
          //   elevation: 6,
          //   onPressed: () => Get.toNamed("/admin-add-city"),
          //   child: const Icon(Icons.location_on_outlined),
          // ),
        ],
      ),
      bottomNavigationBar: AdminBottomNavBar(controller),
    );
  }
}
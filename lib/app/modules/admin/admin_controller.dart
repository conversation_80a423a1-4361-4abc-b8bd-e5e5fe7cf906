import 'package:dostafka/app/modules/global/controllers/language_controller.dart';
import 'package:dostafka/app/modules/global/controllers/state_controller.dart';
import 'package:dostafka/app/modules/global/model/order_model.dart';
import 'package:dostafka/app/modules/home/<USER>/order_provider.dart';
import 'package:dostafka/app/modules/login/login_request_provider.dart';
import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../global/utils/general_utils.dart';

class AdminController extends GetxController {

  final stateController = Get.find<StateController>();
  final loginProvider = Get.find<LoginProvider>();
  final orderProvider = Get.find<OrderProvider>();
  final languageController = Get.find<LanguageController>();
  final view = 'ORDER_TODAY_NEW'.obs;
  final activeOrders = Rx<List<AdminOrder>>([]);
  final finishedOrders = Rx<List<AdminOrder>>([]);
  final historicalOrders = Rx<List<AdminOrder>>([]);
  final lastViewed = 0.obs;

  final showFilters = false.obs;
  final isLoading = false.obs;
  final flushed = false.obs;
  final showWaiting = true.obs;
  final showComing = true.obs;
  final showPicked = true.obs;
  final showDelivery = true.obs;
  final showSending = true.obs;
  final showAdjusted = true.obs;
  final showNotAdjusted = true.obs;

  void logout() {
    var user = stateController.getUser();
    loginProvider.logout(stateController.getDeviceId(), user.phone);
    user.authorized = false;
    user.token = null;
    user.phone = null;
    stateController.setUser(user);
    Get.toNamed('/login');
  }

  @override
  void onInit() async {
    loadActiveOrders();
    loadFinishedOrders();
  }

  Future<bool?> loadActiveOrders() async {
    isLoading(true);
    orderProvider
        .getActive()
        .then((value){
          if (value!=null){
            activeOrders(value);
          }
          return value!=null;
        })
        .whenComplete(() => isLoading(false));
    return null;
  }

  Future<bool?> loadFinishedOrders() async {
    isLoading(true);
    orderProvider
        .getFinished()
        .then((value){
      if (value!=null){
        finishedOrders(value);
      }
      return value!=null;
    })
        .whenComplete(() => isLoading(false));
    return null;
  }

  Future<bool> delete(int orderId) {
    return orderProvider.deleteOrder(orderId)
        .then(
            (value){
              if (value){
                showSnackbar(
                    LocaleKeys.message_success_header.tr,
                    LocaleKeys.message_success_body.tr
                );
              } else {
                showSnackbar(
                  LocaleKeys.message_error_header.tr,
                  LocaleKeys.message_error_body.tr
                );
              }
              return value;
            }
    );
  }

  Future<bool?> loadArchived(String start, String end) async {
    return await orderProvider.getArchive(start, end)
        .then((value){
          if (value!=null){
            historicalOrders(value);
          }
          return value!=null;
        })
        .whenComplete(() => isLoading(false));
  }


}

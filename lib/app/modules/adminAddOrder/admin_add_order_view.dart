
import 'package:dostafka/app/modules/global/components/button_widget.dart';
import 'package:dostafka/app/modules/global/components/input_widget.dart';
import 'package:dostafka/app/modules/global/components/phone_widget.dart';
import 'package:dostafka/app/modules/global/components/v2/city_area_select.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../global/components/btn_widget.dart';
import '../global/components/label_widget.dart';
import '../global/constants.dart';
import '../global/utils/general_utils.dart';
import 'admin_add_order_controller.dart';

class AdminAddOrderView extends GetView<AdminAddOrderController> {

  final phoneRegex = RegExp("^[+\\-]?\\d?\\d{10}\$");
  AdminAddOrderView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: true,
          title:
          const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor,
        ),
        body: SafeArea(
          maintainBottomViewPadding: true,
          child: Column (
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Center(
                child: Label(LocaleKeys.order_title.tr, size: 20, bold: true,),
              ),
              Expanded(
                child: SingleChildScrollView(
                  key: const ValueKey('SingleChildScrollView'),
                  physics: const AlwaysScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      CitySelect(controller.userOrderController),
                      const SizedBox(height: 10,),
                      AreaSelect(controller.userOrderController),
                      const SizedBox(height: 10,),
                      TextInput(controller: controller.userOrderController.addressController, label: LocaleKeys.common_address.tr),
                      const SizedBox(height: 10,),
                      PhoneInput(controller: controller.userOrderController.phoneController,),
                      const SizedBox(height: 10,),
                      TextInput(controller: controller.userOrderController.priceController, label: LocaleKeys.order_cost_title.tr, keyboard: TextInputType.number),
                      const SizedBox(height: 10,),
                      TextFormField(
                        decoration: InputDecoration(
                          focusedBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.white70, width: 2),
                          ),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide:
                            BorderSide(color: Colors.lightGreen, width: 2),
                          ),
                          border: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.white70, width: 2),
                          ),
                          labelText: LocaleKeys.order_comment.tr,
                          fillColor: Globals.fillColor,
                          filled: true,
                        ),
                        controller: controller.userOrderController.commentController,
                        minLines: 4,
                        maxLines: 6,
                        maxLength: 2048,
                        keyboardType: TextInputType.text,
                      ),
                      Obx(() => (controller.userOrderController.files.isEmpty)
                          ? const SizedBox()
                          : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: controller.userOrderController.files
                            .map((f) => Card(
                          surfaceTintColor: Colors.white,
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                width: 120,
                                child: Text(f.key.name,
                                    maxLines: 5,
                                    overflow: TextOverflow.ellipsis),
                              )
                              ,
                              f.value != null
                                  ? Image.memory(f.value!, width: 165)
                                  : const SizedBox(),
                              IconButton(
                                  onPressed: () => controller.userOrderController
                                      .removeImage(f.key.name),
                                  icon: const Icon(Icons.delete_forever))
                            ],
                          ),
                        ))
                            .toList(),
                      )),
                      Button(
                          onPressFunction: () => controller.userOrderController.pickImage(),
                          label: LocaleKeys.button_addPhoto.tr),
                      const SizedBox(height: 25),
                      Obx(()=>
                      controller.isProcessing()
                          ? const CircularProgressIndicator(color: Globals.primeColor,)
                          : Btn(
                          onPressFunction: controller.isReady()
                              ? (){
                                  if (controller.userOrderController.priceController.text.isNumericOnly){
                                    if (phoneRegex.hasMatch(controller.userOrderController.phoneController.text)){
                                      controller.isProcessing(true);
                                      if (controller.userOrderController.files.isEmpty){
                                        showSnackbar("No photo added", "Please add photo");
                                      } else {
                                        controller.userOrderController.sendAdminOrder(context)
                                            .then((value) => value == null
                                            ? showSnackbar(LocaleKeys.message_error_header.tr, LocaleKeys.message_error_body.tr)
                                            : showSnackbar(LocaleKeys.message_success_header.tr, LocaleKeys.message_success_body.tr)
                                        )
                                            .whenComplete((){
                                              controller.isProcessing(false);
                                              Get.close(1);
                                            });
                                      }
                                    } else {
                                      showSnackbar("Invalid phone number", "Enter correct phone");
                                    }
                                  } else {
                                    showSnackbar("Invalid cost", "Enter correct cost");
                                  }
                              }
                              : null,
                          label: "${LocaleKeys.button_add.tr} ${LocaleKeys.order_title.tr}"
                      )
                      )
                    ],
                  ),
                ),
              ),

            ],
          )
        )
    );
  }
}

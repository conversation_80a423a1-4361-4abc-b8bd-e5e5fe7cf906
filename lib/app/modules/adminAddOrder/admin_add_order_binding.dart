import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:dostafka/app/modules/home/<USER>/city_provider.dart';
import 'package:dostafka/app/modules/home/<USER>/order_provider.dart';
import 'package:get/get.dart';

import 'admin_add_order_controller.dart';

class AdminAddOrderBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AdminAddOrderController>(
      () => AdminAddOrderController(),
    );
    Get.lazyPut<CityProvider>(
      () => CityProvider()
    );
    Get.lazyPut<OrderProvider>(
      () => OrderProvider()
    );
    Get.lazyPut<UserOrderController>(
      () => UserOrderController()
    );

  }
}

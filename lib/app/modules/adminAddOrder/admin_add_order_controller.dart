import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:get/get.dart';

class AdminAddOrderController extends GetxController {

  final userOrderController = Get.find<UserOrderController>();
  final isProcessing = false.obs;
  final isReady = false.obs;

  @override
  void onReady() {
    userOrderController.addressController
        .addListener(() => isReady(userOrderController.phoneController.text.isNotEmpty && userOrderController.addressController.text.isNotEmpty)
    );
    userOrderController.phoneController
        .addListener(() => isReady(userOrderController.phoneController.text.isNotEmpty && userOrderController.addressController.text.isNotEmpty)
    );
  }
}

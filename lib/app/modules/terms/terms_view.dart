import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/generated/locales.g.dart';

import '../global/constants.dart';
import 'terms_controller.dart';

class TermsView extends GetView<TermsController> {
  const TermsView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        actions: null,
        leading: null,
        elevation: 5,
        toolbarHeight: 150,
        automaticallyImplyLeading: false,
        backgroundColor: Globals.primeColor,
        title: const Image(
          image: AssetImage("assets/logo2.png"),
          width: 400,
          height: 200,
        ),
      ),
      body: SafeArea(
        maintainBottomViewPadding: true,
        child: Column(
          children: [
            const SizedBox(height: 20),
            Text(LocaleKeys.termAndConditions_title.tr,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 25)),
            const SizedBox(height: 20),
            Expanded(
                // Wrap SingleChildScrollView with Expanded
                child: Scrollbar(
              trackVisibility: true,
              thickness: 8,
              thumbVisibility: true,
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.only(right: 20, left: 20, bottom: 35),
                child: Obx(
                  () => Text(
                    controller.agreement.value,
                    style: const TextStyle(
                      fontSize: 16.0,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            )),
            TextButton(
              onPressed: () => Get.back(),
              child: Text(LocaleKeys.button_close.tr,
                  style: const TextStyle(fontSize: 18)),
            ),
          ],
        ),
      ),
    );
  }
}

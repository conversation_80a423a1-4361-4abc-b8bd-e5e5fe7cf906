import 'dart:developer';

import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../global/controllers/language_controller.dart';

class TermsController extends GetxController {
  final languageController = Get.find<LanguageController>();
  final agreement = "".obs;

  @override
  void onReady() {
    super.onReady();
    loadAgreement().then((value) => agreement(value));
  }

  Future<String> loadAgreement() async {
    log("Current lang: ${languageController.getLocale.toString()}");
    try {
      return rootBundle.loadString(
          "assets/terms/terms_${languageController.getLocale.toString()}.txt");
    } on Exception catch (_) {
      log("Cannot load terms!");
    } catch (_) {
      log("Cannot load terms!");
    }
    return rootBundle.loadString("assets/terms/terms_ru.txt");
  }
}

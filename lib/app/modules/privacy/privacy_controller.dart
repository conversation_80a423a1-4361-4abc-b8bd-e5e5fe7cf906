import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../global/controllers/language_controller.dart';

class PrivacyController extends GetxController {
  final languageController = Get.find<LanguageController>();
  final agreement = "".obs;

  @override
  void onReady() {
    super.onReady();
    loadAgreement().then((value) => agreement(value));
  }

  Future<String> loadAgreement() async {
    log("Current lang: ${languageController.getLocale.toString()}");
    try {
      return rootBundle.loadString(
          "assets/terms/privacy_${languageController.getLocale.toString()}.html");
    } on Exception catch (_) {
      log("Cannot load terms!");
    } catch (_) {
      log("Cannot load terms!");
    }
    return rootBundle.loadString("assets/terms/privacy_ru.html");
  }
}

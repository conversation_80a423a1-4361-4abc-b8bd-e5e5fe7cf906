import 'package:flutter/material.dart';

import 'package:easy_dialog/easy_dialog.dart';
import 'package:get/get.dart';

void showSnackbar(String title, String message) {
  Get.showSnackbar(GetSnackBar(
      title: title, message: message, duration: const Duration(seconds: 3)));
}

void showEasyDialog(BuildContext context, String title, String text,
    double height, double width) {
  EasyDialog(
    title: Text(
      title,
      style: const TextStyle(fontWeight: FontWeight.bold),
      textScaleFactor: 1.2,
    ),
    description: Text(
      text,
      textScaleFactor: 1.1,
      textAlign: TextAlign.center,
    ),
    height: height,
    width: width,
    titlePadding: const EdgeInsets.only(top: 10, bottom: 5),
    descriptionPadding: const EdgeInsets.only(top: 20),
  ).show(context);
}

void showWidgetDialog(BuildContext context, String title,
    double height, double width, List<Widget> children) {
  EasyDialog(
    title: Text(
      title,
      style: const TextStyle(fontWeight: FontWeight.bold),
      textScaleFactor: 1.2,
    ),
    contentListAlignment: CrossAxisAlignment.start,
    contentList: children,
    height: height,
    width: width,
    titlePadding: const EdgeInsets.only(top: 10, bottom: 5),
    descriptionPadding: const EdgeInsets.only(top: 20),
  ).show(context);
}

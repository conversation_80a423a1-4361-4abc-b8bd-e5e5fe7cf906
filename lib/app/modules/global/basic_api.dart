import 'package:get/get.dart';

import '../global/constants.dart';
import '../global/controllers/state_controller.dart';

class BasicAPI extends GetConnect {
  final stateController = Get.find<StateController>();

  Map<String, String> getHeaders() {
    return {
      'Authorization': "Bearer ${stateController.token}",
      'deviceId': stateController.getDeviceId(),
      'contentType': 'application/json',
    };
  }

  void updateHeaders() {
    final headers = getHeaders();
    httpClient.addAuthenticator<dynamic>((request) {
      request.headers.addAll(headers);
      return request;
    });
  }

  BasicAPI() {
    httpClient.baseUrl = Globals.baseUrl;
    httpClient.timeout = const Duration(minutes: 1);
    updateHeaders();
  }

  @override
  void onReady() {
    super.onReady();
    httpClient.baseUrl = Globals.baseUrl;
    httpClient.timeout = const Duration(minutes: 3);
    updateHeaders();
  }
}

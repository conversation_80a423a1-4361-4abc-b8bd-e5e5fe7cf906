class City {
  final String id;
  final String nameRu;
  final String nameKz;
  final String nameEn;
  final int cost;
  final List<Area> areas;

  City(
      {required this.id,
      required this.nameRu,
      required this.nameKz,
      required this.nameEn,
      required this.cost,
      required this.areas});

  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      id: json['id'],
      nameRu: json['nameRu'],
      nameKz: json['nameKz'],
      nameEn: json['nameEn'],
      cost: json['cost'],
      areas: List<Area>.from(json['areas'].map((s) => Area.fromJson(s))),
    );
  }

  // Add a toJson method to convert City to JSON.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nameRu': nameRu,
      'nameKz': nameKz,
      'nameEn': nameEn,
      'cost': cost,
      'areas': areas.map((area) => area.toJson()).toList(),
    };
  }
}

class Area {
  final String id;
  final String nameRu;
  final String nameKz;
  final String nameEn;
  final int cost;

  Area({
    required this.id,
    required this.nameRu,
    required this.nameKz,
    required this.nameEn,
    required this.cost,
  });

  factory Area.fromJson(Map<String, dynamic> json) {
    return Area(
      id: json['id'],
      nameRu: json['nameRu'],
      nameKz: json['nameKz'],
      nameEn: json['nameEn'],
      cost: json['cost'],
    );
  }

  // Add a toJson method to convert Area to JSON.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nameRu': nameRu,
      'nameKz': nameKz,
      'nameEn': nameEn,
      'cost': cost,
    };
  }
}

class LoginResponse {
  int? id;
  String? key;
  String? userType;
  String? token;

  LoginResponse({this.id, this.key, this.userType, this.token});

  LoginResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    key = json['key'];
    userType = json['userType'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['key'] = key;
    data['userType'] = userType;
    data['token'] = token;
    return data;
  }
}

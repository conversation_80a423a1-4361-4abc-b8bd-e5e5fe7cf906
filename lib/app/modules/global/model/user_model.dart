import 'dart:convert';

class UserModel {
  int id;
  String type;
  String? phone;
  String? token;
  bool authorized = false;

  UserModel(
      {required this.type,
      required this.phone,
      required this.token,
      required this.authorized,
      required this.id});

  @override
  String toString() {
    return '{"type": "$type", "phone": "$phone", "token": "$token", "authorized": $authorized, "id":$id}';
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      type: json['type'],
      phone: json['phone'],
      token: json['token'],
      authorized: json['authorized'],
    );
  }

  factory UserModel.fromString(String value) {
    return UserModel.fromJson(const JsonDecoder().convert(value));
  }
}

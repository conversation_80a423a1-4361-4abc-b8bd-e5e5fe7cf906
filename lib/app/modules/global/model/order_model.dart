class ReceivingOrder {
  final String cityId;
  final String areaId;
  final String receiverAddress;
  final String receiverPhone;

  final String truckNumber;
  final String truckContact;
  final String truckPhone;

  ReceivingOrder(
      {required this.cityId,
      required this.areaId,
      required this.receiverAddress,
      required this.receiverPhone,
      required this.truckNumber,
      required this.truckContact,
      required this.truckPhone});
}

class SubmittedOrder {
  final int orderId;
  final String orderDate;
  final String name;
  final int count;
  final List<String> imagesId;

  SubmittedOrder(
    {
      required this.orderId,
      required this.orderDate,
      required this.name,
      required this.count,
      required this.imagesId
    }
  );

  factory SubmittedOrder.fromJson(Map<String, dynamic> json) {
    return SubmittedOrder(
      orderId: json['orderId'],
      orderDate: json['orderDate'],
      name: json['name'],
      count: json['count'],
      imagesId: (json['imagesId'] as List<dynamic>).cast<String>(),
    );
  }
}

class MyOrder {
  final int orderId;
  final String orderDate;
  final String status;
  final int cost;
  final bool adjusted;
  final String type;
  final String address;
  final String comment;
  final String truckNumber;
  final int count;
  final List<String> imagesId;

  MyOrder({
    required this.orderId,
    required this.orderDate,
    required this.status,
    required this.cost,
    required this.adjusted,
    required this.type,
    required this.address,
    required this.comment,
    required this.truckNumber,
    required this.count,
    required this.imagesId,
  });

  factory MyOrder.fromJson(Map<String, dynamic> json) {
    return MyOrder(
      orderId: json['orderId'],
      orderDate: json['orderDate'],
      status: json['orderStatus'],
      cost: json['cost'],
      adjusted: json['adjusted'],
      type: json['type'],
      address: json['address'],
      comment: json['comment'],
      truckNumber: json['truckNumber'],
      count: json['count'],
      imagesId: (json['imagesId'] as List<dynamic>).cast<String>(),
    );
  }
}

class AdminOrder {
  final int orderId;
  final String orderDate;
  String status;
  int cost;
  bool adjusted;
  final String cityArea;
  final String address;
  final String phone;
  final String comment;
  final String truckNumber;
  final String truckPhone;
  final String type;
  final String name;
  final int count;
  final List<String> imagesId;

  AdminOrder({
    required this.orderId,
    required this.orderDate,
    required this.status,
    required this.cost,
    required this.adjusted,
    required this.cityArea,
    required this.address,
    required this.phone,
    required this.comment,
    required this.truckNumber,
    required this.truckPhone,
    required this.type,
    required this.name,
    required this.count,
    required this.imagesId,
  });

  factory AdminOrder.fromJson(Map<String, dynamic> json) {
    return AdminOrder(
      orderId: json['orderId'],
      orderDate: json['orderDate'],
      status: json['orderStatus'],
      cost: json['cost'],
      adjusted: json['adjusted'],
      cityArea: json['cityArea'],
      address: json['address'],
      phone: json['phone'],
      comment: json['comment'],
      truckNumber: json['truckNumber'],
      truckPhone: json['truckPhone'],
      type: json['type'],
      name: json['name'],
      count: json['count'],
      imagesId: (json['imagesId'] as List<dynamic>).cast<String>(),
    );
  }
}


import 'package:get/get.dart';

class LifeCycleController extends SuperController {
  final RxString appSate = ''.obs;

  RxString getAppState() => appSate;

  @override
  void onDetached() {
    appSate('Detached');
  }

  @override
  void onInactive() {
    appSate('Inactive');
  }

  @override
  void onPaused() {
    appSate('Paused');
  }

  @override
  void onResumed() {
    appSate('Resumed');
  }

  @override
  void onHidden() {
    appSate('Hidden');
  }
}

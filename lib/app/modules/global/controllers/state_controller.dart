import 'dart:developer';


import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:uuid/uuid.dart';

import '../../../modules/splash/info_response_model.dart';
import '../model/address_model.dart';
import '../model/user_model.dart';

class StateController extends GetxController {
  static StateController get to => Get.find<StateController>();
  final _offline = false.obs;
  final _user = UserModel(
          type: 'NONE', token: null, phone: null, authorized: false, id: -1)
      .obs;
  final _initialLoading = false.obs;
  final _storage = GetStorage();
  final _deviceId = const Uuid().v4().toString().obs;
  final _serverInfo = Rxn<InfoResponse>(null);
  final role = 'NONE'.obs;
  final token = Rxn<String>(null);
  final clientId = Rxn<int>(null);

  @override
  void onInit() {
    super.onInit();
    if (_storage.hasData('deviceId')) {
      _deviceId(_storage.read('deviceId'));
    } else {
      _storage.write('deviceId', _deviceId.value);
    }
    log("Device ID: ${_deviceId.value}");
    if (_storage.hasData('userInfo')) {
      _user(UserModel.fromString(_storage.read('userInfo')));
      clientId(_user.value.id);
      role(_user.value.type);
      token(_user.value.token);
    } else {
      _storage.write('userInfo', _user.value.toString());
    }
    log("UserInfo: ${_user.value.toString()}");
    _initialLoading(true);
  }

  void setOffline(bool offlineStatus) {
    _offline(offlineStatus);
  }

  bool isOffline() {
    return _offline.value;
  }

  void setServerInfo(InfoResponse info) {
    _serverInfo(info);
  }

  InfoResponse? getServerInfo() {
    return _serverInfo.value;
  }

  void setUser(UserModel userModel) {
    _user(userModel);
    _storage.write('userInfo', userModel.toString());
    clientId(userModel.id);
    role(userModel.type);
    token(userModel.token);
  }

  UserModel getUser() {
    return _user.value;
  }

  String getDeviceId() {
    return _deviceId.value;
  }

  bool isInitialLoadingDone() {
    return _initialLoading.value;
  }

  List<City>? getCities() {
    if (_storage.hasData('cities')) {
      try {
        return _storage.read('cities');
      } catch (e){
        log("Unable to read stored cities");
      }
    }
    return null;
  }

  void setCities(List<City> cities) {
    _storage.write('cities', cities);
  }

  getFcmToken() {
    return _storage.read("fcm_token");
  }

  GetStorage getStorage() {
    return _storage;
  }
}

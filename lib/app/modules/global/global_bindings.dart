import 'package:get/get.dart';

import '../global/controllers/language_controller.dart';
import '../global/controllers/state_controller.dart';
import 'controllers/app_state_controller.dart';

class GlobalBindings extends Bindings {
  @override
  void dependencies() {
    Get.put<StateController>(StateController());
    Get.put<LanguageController>(LanguageController());
    Get.put<LifeCycleController>(LifeCycleController());
  }
}

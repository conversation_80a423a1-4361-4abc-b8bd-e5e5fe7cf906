import 'package:expandable/expandable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class Globals {
  static const defaultLanguage = 'ru';

  static const String baseUrl =
     kReleaseMode ? "https://dostafka.kz:8443" : "http://192.168.2.39:8099"; // MAC
    //   kReleaseMode ? "https://dostafka.kz:8443" : "http://192.168.2.39:8099";  // Windows

  static const String paymentUrl = "https://pay.kaspi.kz/pay/24aloksi";
  static const primeColor = Color.fromRGBO(95, 135, 10, 1.0);
  // static const primeColor = Color.fromRGBO(91, 169, 107, 1.0);
  static const fillColor = Color.fromRGBO(212, 252, 223, 0.5);
  static const btnColor = Color.fromRGBO(212, 252, 223, 1);

  static const defaultBackgroundColor = Colors.white;

  static const expandableThemeData = ExpandableThemeData(
      tapBodyToExpand: true,
      tapHeaderToExpand: true,
      hasIcon: true,
      collapseIcon: Icons.arrow_drop_down_sharp,
      expandIcon: Icons.arrow_drop_up_sharp,
      iconColor: Globals.primeColor
  );

}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../constants.dart';

class PhoneInput extends StatelessWidget {

  final TextEditingController controller;

  final showIcon = false.obs;
  final FocusNode focusNode = FocusNode();


  PhoneInput({super.key, required this.controller, text}){
    if (text!=null){
      controller.text=text;
    }
    focusNode.addListener((){
       if (focusNode.hasFocus || controller.value.text != ''){
         showIcon(true);
       } else {
         showIcon(false);
       }
    });

  }


  final inputType = const TextInputType.numberWithOptions(decimal: false, signed: false);
  final formatter = <TextInputFormatter>[ FilteringTextInputFormatter.digitsOnly ];
  final textStyle =  const TextStyle(fontSize: 20);
  final inputDecoration = InputDecoration(
      counterText: "",
      focusedBorder: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.white70, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      enabledBorder: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.lightGreen, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      border: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.white70, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      labelText: LocaleKeys.common_phone.tr,
      fillColor: Globals.fillColor,
      filled: true
  );
  final prefixInputDecoration = InputDecoration(
      counterText: "",
      focusedBorder: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.white70, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      enabledBorder: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.lightGreen, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      border: const UnderlineInputBorder(
        borderSide: BorderSide(color: Colors.white70, width: 2),
        // borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      labelText: LocaleKeys.common_phone.tr,
      fillColor: Globals.fillColor,
      prefixIcon: const Padding(
        padding: EdgeInsets.only(left: 15, top: 22),
        child: Text("+7", style: TextStyle(fontSize: 20)),
      ),
      filled: true
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() =>
      showIcon.value
        ? TextFormField(
            maxLength: 10,
            controller: controller,
            focusNode: focusNode,
            onChanged: (value) => controller.text,
            style: textStyle,
            autocorrect: false,
            keyboardType: inputType,
            inputFormatters: formatter,
            decoration: prefixInputDecoration,
          )
        : TextFormField(
            maxLength: 10,
            controller: controller,
            focusNode: focusNode,
            onChanged: (value) => controller.text,
            style: textStyle,
            autocorrect: false,
            keyboardType: inputType,
            inputFormatters: formatter,
            decoration: inputDecoration,
          )
    );
  }
}

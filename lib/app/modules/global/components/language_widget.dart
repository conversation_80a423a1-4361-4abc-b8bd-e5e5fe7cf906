import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../modules/global/controllers/language_controller.dart';

class LanguageSelect extends StatelessWidget {
  final LanguageController languageController;
  final showText = false.obs;

  LanguageSelect({required this.languageController, showText, super.key}) {
    if (showText != null) {
      this.showText(showText);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: showText.value
          ? MainAxisAlignment.spaceBetween
          : MainAxisAlignment.center,
      children: [
        Obx(() => showText.value
            ? Text(
                LocaleKeys.common_language.tr,
                style: const TextStyle(fontSize: 18, color: Colors.white70),
              )
            : const SizedBox(width: 1.0)),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              iconSize: 40,
              icon: const Image(
                image: AssetImage("assets/flag_ru.png"),
                width: 50,
                height: 50,
              ),
              onPressed: () => {languageController.updateLanguage("ru")},
            ),
            IconButton(
              iconSize: 40,
              icon: const Image(
                image: AssetImage("assets/flag_kz.png"),
                width: 50,
                height: 50,
              ),
              onPressed: () => {languageController.updateLanguage("kz")},
            ),
            IconButton(
              iconSize: 40,
              icon: const Image(
                image: AssetImage("assets/flag_en.png"),
                width: 50,
                height: 50,
              ),
              onPressed: () => {languageController.updateLanguage("en")},
            ),
          ],
        )
      ],
    );
  }
}

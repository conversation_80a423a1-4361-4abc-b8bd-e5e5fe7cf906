import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:dostafka/app/modules/home/<USER>';

import '../../../../generated/locales.g.dart';
import '../../admin/admin_controller.dart';
import '../constants.dart';

class UserDrawer extends StatelessWidget {
  final HomeController homeController;
  const UserDrawer({super.key, required this.homeController});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Material(
        color: Globals.primeColor,
        child: ListView(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                children: [
                  const SizedBox(height: 24),
                  // const SizedBox(height: 12),
                  MenuItem(
                    text: LocaleKeys.drawer_profile.tr,
                    icon: Icons.people,
                    onClicked: () {
                      Get.close(1);
                      Get.toNamed('/profile');
                    },
                  ),
                  const SizedBox(height: 5),
                  MenuItem(
                      text: LocaleKeys.drawer_order.tr,
                      icon: Icons.add_to_photos_outlined,
                      onClicked: () {
                        homeController.view('ORDER_NEW');
                        Get.close(1);
                      }),
                  const SizedBox(height: 5),
                  MenuItem(
                      text: LocaleKeys.drawer_orders.tr,
                      icon: Icons.table_view,
                      onClicked: () {
                        homeController.view('ORDER_LIST');
                        Get.close(1);
                      }),
                  const SizedBox(height: 8),
                  const Divider(color: Colors.white70),
                  const SizedBox(height: 8),
                  MenuItem(
                    text: LocaleKeys.menu_notification.tr,
                    icon: Icons.notifications_outlined,
                    onClicked: () {
                      homeController.view('HOME');
                      Get.close(1);
                    },
                  ),
                  MenuItem(
                    text: LocaleKeys.menu_privacy.tr,
                    icon: Icons.privacy_tip,
                    onClicked: () {
                      Get.close(1);
                      Get.toNamed('/privacy');
                    },
                  ),
                  MenuItem(
                      text: LocaleKeys.button_logout.tr,
                      icon: Icons.logout,
                      onClicked: () {
                        homeController.view("HOME");
                        homeController.logout();
                      }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AdminDrawer extends StatelessWidget {
  final AdminController adminController;
  const AdminDrawer(this.adminController, {super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Material(
        color: Globals.primeColor,
        child: ListView(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(15.0),
              child: Column(
                children: [
                  const SizedBox(height: 24),
                  MenuItem(
                    text: LocaleKeys.drawer_profile.tr,
                    icon: Icons.people,
                    onClicked: () {
                      Get.close(1);
                      Get.toNamed('/profile');
                    },
                  ),
                  const SizedBox(height: 5),
                  MenuItem(
                      text: LocaleKeys.drawer_order.tr,
                      icon: Icons.add_to_photos_outlined,
                      onClicked: () {
                        Get.close(1);
                        Get.toNamed('/admin-add-order');
                      }),
                  const SizedBox(height: 5),
                  MenuItem(
                      text: LocaleKeys.menu_notification.tr,
                      icon: Icons.notifications_outlined,
                      onClicked: () {
                        Get.close(1);
                        Get.toNamed('/admin-notification');
                      }),
                  const SizedBox(height: 5),
                  MenuItem(
                    text: LocaleKeys.menu_restorePassword.tr,
                    icon: Icons.lock_open,
                    onClicked: (){
                      Get.close(1);
                      Get.toNamed('/restore-password');
                    },
                  ),
                  const SizedBox(height: 8),
                  const Divider(color: Colors.white70),
                  const SizedBox(height: 8),
                  MenuItem(
                    text: LocaleKeys.button_logout.tr,
                    icon: Icons.logout,
                    onClicked: () => adminController.logout(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MenuItem extends StatelessWidget {
  final String text;
  final IconData icon;
  final VoidCallback? onClicked;

  const MenuItem({
    required this.text,
    required this.icon,
    this.onClicked,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const color = Colors.white;
    const hoverColor = Colors.white70;

    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(text, style: const TextStyle(color: color)),
      hoverColor: hoverColor,
      onTap: onClicked,
    );
  }
}

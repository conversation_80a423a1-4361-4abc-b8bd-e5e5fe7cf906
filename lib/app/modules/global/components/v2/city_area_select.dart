import 'package:dostafka/app/modules/home/<USER>/user/user_order_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../generated/locales.g.dart';
import '../../constants.dart';
import '../../model/address_model.dart';

class CitySelect extends StatelessWidget {
  final UserOrderController userOrderController;

  const CitySelect(this.userOrderController, {super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
        decoration: const BoxDecoration(color: Globals.fillColor),
        height: 65,
        child: DropdownButton<City>(
          value: userOrderController.city.value,
          hint: Text(LocaleKeys.common_city.tr),
          onChanged: userOrderController.onCitySelected,
          items: userOrderController.cities.map((City city) {
            return DropdownMenuItem<City>(
              value: city,
              child: Text(userOrderController.getCityName(city)),
            );
          }).toList(),
          isExpanded: true,
          elevation: 3,
        )));
  }
}

class AreaSelect extends StatelessWidget {
  final UserOrderController userOrderController;

  const AreaSelect(this.userOrderController, {super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
        decoration: const BoxDecoration(color: Globals.fillColor),
        height: 65,
        child: DropdownButton<Area>(
          value: userOrderController.area.value,
          hint: Text(LocaleKeys.common_city.tr),
          onChanged: userOrderController.onAreaSelected,
          items: userOrderController.areas.map((Area area) {
            return DropdownMenuItem<Area>(
              value: area,
              child: Text(userOrderController.getAreaName(area)),
            );
          }).toList(),
          isExpanded: true,
          elevation: 3,
        )));
  }
}

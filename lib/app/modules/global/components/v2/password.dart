
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants.dart';

class Password extends StatelessWidget {

  final showPassword = false.obs;
  final String label;
  final TextEditingController controller;

  Password({super.key, required this.controller, required this.label,});

  @override
  Widget build(BuildContext context) {
    return Obx(() => TextFormField(
      controller: controller,
      style: const TextStyle(fontSize: 20),
      obscureText: !showPassword.value,
      autocorrect: false,
      maxLines: 1,
      keyboardType: TextInputType.text,
      decoration: InputDecoration(
          focusedBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.white70, width: 2),
          ),
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.lightGreen, width: 2),
          ),
          border: const UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.white70, width: 2),
          ),
          labelText: label,
          fillColor: Globals.fillColor,
          filled: true,
        suffixIcon: IconButton(
            onPressed: () => showPassword(!showPassword.value),
            icon: Icon(showPassword.value ? Icons.visibility : Icons.visibility_off),
        )
      )
      )
    );
  }
}


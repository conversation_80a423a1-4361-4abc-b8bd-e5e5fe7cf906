import 'package:flutter/material.dart';

import '../constants.dart';

class TextInput extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  bool hide;
  TextInputType keyboard;
  Function? validator;

  TextInput(
      {required this.controller,
      required this.label,
      this.hide = false,
      this.keyboard = TextInputType.text,
      this.validator,
      super.key});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      style: const TextStyle(fontSize: 20),
      obscureText: hide,
      autocorrect: false,
      maxLines: 1,
      keyboardType: keyboard,
      validator: validator != null
          ? (value) => validator!(value)
          : null ,
      autovalidateMode: validator!=null
          ? AutovalidateMode.onUserInteraction
          : AutovalidateMode.disabled,
      decoration: InputDecoration(
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white70, width: 2),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.lightGreen, width: 2),
        ),
        border: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white70, width: 2),
        ),
        labelText: label,
        fillColor: Globals.fillColor,
        filled: true,
      ),
    );
  }
}

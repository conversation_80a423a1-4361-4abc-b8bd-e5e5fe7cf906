

import 'package:flutter/material.dart';

class LabeledText extends StatelessWidget {

  final String text;
  final String label;


  const LabeledText(this.text, this.label, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 9)),
        Text(text, style: const TextStyle(fontSize: 12), overflow: TextOverflow.fade,)
      ],
    );
  }
}
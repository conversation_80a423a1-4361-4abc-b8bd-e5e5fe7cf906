import 'package:flutter/material.dart';

import '../constants.dart';

class Label extends StatelessWidget {
  final String text;
  final double size;
  final bool bold;
  const Label(this.text, {this.size = 18.0, this.bold = false, super.key});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
          color: Globals.primeColor,
          fontSize: size,
          fontWeight: bold ? FontWeight.bold : FontWeight.normal),
    );
  }
}

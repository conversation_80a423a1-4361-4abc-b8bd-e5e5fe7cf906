import 'package:flutter/material.dart';

import 'package:get/get.dart';

class ExpendedCard extends StatelessWidget {
  final RxBool expended;
  final Widget simpleChild;
  final Widget expendedChild;
  const ExpendedCard(
      {super.key,
      required this.simpleChild,
      required this.expendedChild,
      required this.expended});

  @override
  Widget build(BuildContext context) {
    return Obx(() => expended.value ? expendedChild : simpleChild);
  }
}

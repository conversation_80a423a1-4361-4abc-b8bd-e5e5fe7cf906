import 'package:flutter/material.dart';

import '../constants.dart';

class But<PERSON> extends StatelessWidget {
  final Function? onPressFunction;
  final String label;
  final double? size;
  final double? fontsize;

  const Button(
      {required this.onPressFunction,
      required this.label,
      this.size,
      this.fontsize,
      super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => onPressFunction != null ? onPressFunction!() : null,
      style: ButtonStyle(
        fixedSize: size != null
            ? MaterialStatePropertyAll(Size.fromWidth(size!))
            : null,
        foregroundColor: onPressFunction == null
            ? const MaterialStatePropertyAll(Colors.grey)
            : const MaterialStatePropertyAll(Globals.primeColor),
        backgroundColor:
            const MaterialStatePropertyAll(Globals.defaultBackgroundColor),
        side: const MaterialStatePropertyAll(
            BorderSide(color: Globals.primeColor, width: 1)),
      ),
      child: Text(label, style: TextStyle(fontSize: fontsize ?? 18)),
    );
  }
}

import 'package:flutter/material.dart';

import '../constants.dart';

class Btn extends StatelessWidget {
  final Function? onPressFunction;
  final String label;

  const Btn({required this.onPressFunction, required this.label, super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => onPressFunction != null ? onPressFunction!() : null,
      style: ButtonStyle(
        foregroundColor: onPressFunction == null
            ? const MaterialStatePropertyAll(Colors.black26)
            : const MaterialStatePropertyAll(Colors.white70),
        backgroundColor: const MaterialStatePropertyAll(Globals.primeColor),
        side: const MaterialStatePropertyAll(
            BorderSide(color: Colors.black, width: 1.5)),
      ),
      child: Text(label, style: const TextStyle(fontSize: 18)),
    );
  }
}

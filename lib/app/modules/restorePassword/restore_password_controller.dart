
import 'package:dostafka/app/modules/global/utils/general_utils.dart';
import 'package:dostafka/app/modules/restorePassword/password_reset_provider.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class RestorePasswordController extends GetxController {

  final provider = Get.find<PasswordResetProvider>();
  final processing = false.obs;
  final ready = false.obs;
  final TextEditingController phoneController = TextEditingController();

  @override
  void onInit(){
    super.onInit();
    phoneController.addListener(() {
      ready(phoneController.text.length > 9);
    });
  }

  void savePassword(){
    processing(true);
    provider.resetPassword(int.parse(phoneController.text))
        .then((value){
            if (value){
              showSnackbar(LocaleKeys.message_success_header.tr, LocaleKeys.message_success_body.tr);
            } else {
              showSnackbar(LocaleKeys.message_error_header.tr, LocaleKeys.message_error_body.tr);
            }
          })
        .whenComplete((){
          processing(false);
          phoneController.clear();
          //Get.close(1);
        });
  }

}

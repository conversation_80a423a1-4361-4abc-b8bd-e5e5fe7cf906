import 'package:dostafka/app/modules/global/components/btn_widget.dart';
import 'package:dostafka/app/modules/global/components/label_widget.dart';
import 'package:dostafka/app/modules/global/components/phone_widget.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../global/constants.dart';
import 'restore_password_controller.dart';

class RestorePasswordView extends GetView<RestorePasswordController> {

  const RestorePasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: true,
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Label(LocaleKeys.menu_restorePassword.tr),
              const SizedBox(height: 25),
              PhoneInput(
                  controller: controller.phoneController
              ),
              const SizedBox(height: 25),
              Obx(()=>
                controller.processing.value
                    ? const CircularProgressIndicator()
                    : Btn(
                        onPressFunction: controller.ready.value
                            ? controller.savePassword
                            : null,
                        label: LocaleKeys.button_restore.tr
                      )
              )
            ],
          ),
        )
      )
    );
  }
}

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../global/constants.dart';
import 'restore_controller.dart';

class RestoreView extends GetView<RestoreController> {
  const RestoreView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: true,
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor
      ),
      body: const Center(
        child: Text(
          'Restore Password view is under development',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}

import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../global/controllers/state_controller.dart';
import 'providers/info_response_provider.dart';

class SplashController extends GetxController {
  final infoResponseProvider = Get.find<InfoResponseProvider>();
  final stateController = Get.find<StateController>();
  final isPolicyDisplayed = true.obs;
  final loaded = false.obs;


  @override
  void onReady()  {
    super.onReady();
    var policyDisplayed = GetStorage().read("policyDisplayed");
    loaded(true);
    if(policyDisplayed==null){
      isPolicyDisplayed(false);
    } else {
      loadServerInfo();
    }
  }

  void loadServerInfo() async {
    infoResponseProvider
        .getInfoResponse(stateController.getDeviceId())
        .then((value) {
      stateController.setOffline(value != null);
      if (value != null) {
        stateController.setServerInfo(value);
        log("Key from server : ${value.key}\nVersion of the server: ${value.version} ");
      }
    }).whenComplete(() {
      if (stateController.getUser().authorized) {
        if (stateController.getUser().type == 'ADMIN' ){
          Get.toNamed('/admin');
        } else {
          Get.toNamed('/home');
        }
      } else {
        Get.toNamed('/login');
      }
    });
  }

  String getDeviceId() {
    return stateController.getDeviceId();
  }

  void agreed() {
    GetStorage().write("policyDisplayed", true);
    isPolicyDisplayed(true);
    loadServerInfo();
  }

}

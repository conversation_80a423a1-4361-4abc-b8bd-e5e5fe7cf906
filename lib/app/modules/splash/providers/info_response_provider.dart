import 'package:get/get.dart';

import '../../global/constants.dart';
import '../info_response_model.dart';

class InfoResponseProvider extends GetConnect {
  @override
  void onInit() {
    httpClient.baseUrl = Globals.baseUrl;
    httpClient.defaultDecoder = (map) {
      if (map is Map<String, dynamic>) return InfoResponse.fromJson(map);
      if (map is List) {
        return map.map((item) => InfoResponse.fromJson(item)).toList();
      }
    };
  }

  Future<InfoResponse?> getInfoResponse(String deviceId) async {
    final response = await get('/public/settings?deviceId=$deviceId');
    return response.body;
  }
}

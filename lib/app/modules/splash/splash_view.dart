import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../global/components/btn_widget.dart';
import '../global/constants.dart';
import 'splash_controller.dart';

class SplashView extends GetView<SplashController> {
  @override
  final SplashController controller = Get.find<SplashController>();

  SplashView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
          child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
            const Image(
              image: AssetImage("assets/logo2.png"),
              width: 400,
              height: 70,
            ),
             Obx(() =>
              controller.loaded.value && !controller.isPolicyDisplayed.value
                 ? SimpleDialog(
               title: Text(LocaleKeys.dialogs_title.tr),
               elevation: 5,
               children: [
                 SizedBox(
                   height: 400,
                   width: MediaQuery.of(context).size.width-20,
                   child: SingleChildScrollView(
                     child: Html(data: LocaleKeys.dialogs_text.tr),
                   ),
                 ),

                 Row(
                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                   children: [
                     Btn(
                         onPressFunction: controller.agreed,
                         label: LocaleKeys.dialogs_agree.tr
                     ),
                   ],
                 )
               ],
             )
             : const CircularProgressIndicator()
             )
          ])),
      backgroundColor: Globals.primeColor,
    );
  }
}

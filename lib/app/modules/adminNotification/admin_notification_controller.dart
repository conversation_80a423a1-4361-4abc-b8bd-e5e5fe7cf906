import 'package:dostafka/app/modules/global/utils/general_utils.dart';
import 'package:dostafka/app/modules/home/<USER>';
import 'package:dostafka/app/modules/home/<USER>/news_response_provider.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class AdminNotificationController extends GetxController {

  final NewsResponseProvider provider = Get.find<NewsResponseProvider>();

  final titleController = TextEditingController();
  final bodyController = TextEditingController();

  final startDate = DateFormat("dd-MM-yyyy").format(DateTime.now()).obs;
  final endDate = DateFormat("dd-MM-yyyy").format(DateTime.now()).obs;
  final isReady = false.obs;
  final processing = false.obs;

  @override
  void onReady() {
    titleController.addListener(
        () => isReady(titleController.text!= '' && bodyController.text != '')
    );

    bodyController.addListener(
        () => isReady(titleController.text!= '' && bodyController.text != '')
    );

  }

   addNotification() async {
    processing(true);
    provider.postNews(
        NotificationRequest(
          startDate: startDate.value,
          endDate: endDate.value,
          title: titleController.text,
          text: bodyController.text
        )
    ).then((value){
      showSnackbar(
          value
              ? LocaleKeys.message_success_header.tr
              : LocaleKeys.message_error_header.tr,
          value
              ? LocaleKeys.message_success_body.tr
              : LocaleKeys.message_error_body.tr,
      );
    })
    .whenComplete(() => processing(false));
  }



}


import 'package:dostafka/app/modules/global/components/btn_widget.dart';
import 'package:dostafka/app/modules/global/components/input_widget.dart';
import 'package:dostafka/app/modules/global/components/label_widget.dart';
import 'package:dostafka/generated/locales.g.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../global/components/button_widget.dart';
import '../global/constants.dart';
import 'admin_notification_controller.dart';

class AdminNotificationView extends GetView<AdminNotificationController> {

  const AdminNotificationView({super.key});

  Future<void> _showStartDatePicker(BuildContext context, RxString date) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        firstDate: DateTime.now(),
        fieldLabelText: LocaleKeys.common_date.tr,
        cancelText: LocaleKeys.button_cancel.tr,
        confirmText: LocaleKeys.button_ok.tr,
        helpText: LocaleKeys.common_date.tr,
        errorFormatText: LocaleKeys.message_error_header.tr,
        errorInvalidText: LocaleKeys.message_error_header.tr,
    );
    if (picked != null) {
      date(DateFormat("dd-MM-yyyy").format(picked));
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: true,
          title: const Image(width: 100, image: AssetImage("assets/logo2.png")),
          centerTitle: true,
          backgroundColor: Globals.primeColor
      ),
      body:
      Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: ListView(
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Label(LocaleKeys.menu_notification.tr, bold: true, size: 20,)
                ],
              ),
              const SizedBox(height: 10),
              Center(
                  child: Label(LocaleKeys.notification_action.tr)
              ),
              const SizedBox(height: 10),
              Text(LocaleKeys.notification_info.tr),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(LocaleKeys.common_dateFrom.tr),
                  Obx(() =>
                      Button(
                          onPressFunction: () => _showStartDatePicker(context, controller.startDate),
                          label: controller.startDate.value
                      )
                  ),
                ],
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(LocaleKeys.common_dateTo.tr),
                  Obx(() =>
                      Button(
                          onPressFunction: () => _showStartDatePicker(context, controller.endDate),
                          label: controller.endDate.value
                      )
                  ),
                ],
              ),
              const SizedBox(height: 10),
              TextInput(controller: controller.titleController, label: LocaleKeys.notification_title.tr),
              const SizedBox(height: 10),
              TextFormField(
                decoration: InputDecoration(
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70, width: 2),
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide:
                    BorderSide(color: Colors.lightGreen, width: 2),
                  ),
                  border: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70, width: 2),
                  ),
                  labelText: LocaleKeys.notification_body.tr,
                  fillColor: Globals.fillColor,
                  filled: true,
                ),
                controller: controller.bodyController,
                minLines: 8,
                maxLines: 20,
                maxLength: 4000,
                keyboardType: TextInputType.text,
              ),
              Center(
                  child: Obx(()=>controller.processing.value
                    ? const CircularProgressIndicator()
                    : Btn(
                    label: LocaleKeys.button_add.tr,
                    onPressFunction: controller.isReady.value
                        ? () => controller.addNotification()
                        : null,
                  )
                ),
              )
            ],
          ),
        ),
    );
  }
}

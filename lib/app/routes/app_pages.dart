import 'package:get/get.dart';

import '../modules/admin/admin_binding.dart';
import '../modules/admin/admin_view.dart';
import '../modules/adminAddCity/admin_add_city_binding.dart';
import '../modules/adminAddCity/admin_add_city_view.dart';
import '../modules/adminAddOrder/admin_add_order_binding.dart';
import '../modules/adminAddOrder/admin_add_order_view.dart';
import '../modules/adminNotification/admin_notification_binding.dart';
import '../modules/adminNotification/admin_notification_view.dart';
import '../modules/home/<USER>';
import '../modules/home/<USER>';
import '../modules/login/login_binding.dart';
import '../modules/login/login_view.dart';
import '../modules/privacy/privacy_binding.dart';
import '../modules/privacy/privacy_view.dart';
import '../modules/profile/profile_binding.dart';
import '../modules/profile/profile_view.dart';
import '../modules/registration/registration_binding.dart';
import '../modules/registration/registration_view.dart';
import '../modules/restore/restore_binding.dart';
import '../modules/restore/restore_view.dart';
import '../modules/restorePassword/restore_password_binding.dart';
import '../modules/restorePassword/restore_password_view.dart';
import '../modules/splash/splash_binding.dart';
import '../modules/splash/splash_view.dart';
import '../modules/terms/terms_binding.dart';
import '../modules/terms/terms_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.TERMS,
      page: () => const TermsView(),
      binding: TermsBinding(),
    ),
    GetPage(
      name: _Paths.REGISTRATION,
      page: () => const RegistrationView(),
      binding: RegistrationBinding(),
    ),
    GetPage(
      name: _Paths.RESTORE,
      page: () => const RestoreView(),
      binding: RestoreBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.ADMIN,
      page: () => AdminView(),
      binding: AdminBinding(),
    ),
    GetPage(
      name: _Paths.ADMIN_NOTIFICATION,
      page: () => const AdminNotificationView(),
      binding: AdminNotificationBinding(),
    ),
    GetPage(
      name: _Paths.ADMIN_ADD_ORDER,
      page: () => AdminAddOrderView(),
      binding: AdminAddOrderBinding(),
    ),
    GetPage(
      name: _Paths.ADMIN_ADD_CITY,
      page: () => const AdminAddCityView(),
      binding: AdminAddCityBinding(),
    ),
    GetPage(
      name: _Paths.PRIVACY,
      page: () => const PrivacyView(),
      binding: PrivacyBinding(),
    ),
    GetPage(
      name: _Paths.RESTORE_PASSWORD,
      page: () => const RestorePasswordView(),
      binding: RestorePasswordBinding(),
    ),
  ];
}

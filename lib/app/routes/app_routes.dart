part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const SPLASH = _Paths.SPLASH;
  static const LOGIN = _Paths.LOGIN;
  static const TERMS = _Paths.TERMS;
  static const REGISTRATION = _Paths.REGISTRATION;
  static const RESTORE = _Paths.RESTORE;
  static const PROFILE = _Paths.PROFILE;
  static const ADMIN_HOME = _Paths.ADMIN_HOME;
  static const ADMIN_LIST = _Paths.ADMIN_LIST;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const ADMIN = _Paths.ADMIN;
  static const ADMIN_NOTIFICATION = _Paths.ADMIN_NOTIFICATION;
  static const ADMIN_ADD_ORDER = _Paths.ADMIN_ADD_ORDER;
  static const ADMIN_ADD_CITY = _Paths.ADMIN_ADD_CITY;
  static const PRIVACY = _Paths.PRIVACY;
  static const RESTORE_PASSWORD = _Paths.RESTORE_PASSWORD;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const SPLASH = '/splash';
  static const LOGIN = '/login';
  static const TERMS = '/terms';
  static const REGISTRATION = '/registration';
  static const RESTORE = '/restore';
  static const PROFILE = '/profile';
  static const ADMIN_HOME = '/admin-home';
  static const ADMIN_LIST = '/admin-list';
  static const NOTIFICATION = '/notification';
  static const ADMIN = '/admin';
  static const ADMIN_NOTIFICATION = '/admin-notification';
  static const ADMIN_ADD_ORDER = '/admin-add-order';
  static const ADMIN_ADD_CITY = '/admin-add-city';
  static const PRIVACY = '/privacy';
  static const RESTORE_PASSWORD = '/restore-password';
}

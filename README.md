# DOSTAFKA

Flutter app for delivery service.

## Getting Started

App is developed with GetX framework, using Getx-cli.

GetX commands:\
Generate page:      ```get create page <page_name>```\
Generate model:     ```get generate mode <path to json>```\
Generate provider:  ```get generate provider <path to model>```\
Generate locale:    ```get generate locales``` 


A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.


If getx does not work - run command: 
```flutter pub global activate -s git https://github.com/jonataslaw/get_cli```

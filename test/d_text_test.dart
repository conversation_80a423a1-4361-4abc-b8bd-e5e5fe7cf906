import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dostafka/app/v2/const/d_size.dart';
import 'package:dostafka/app/v2/const/d_location.dart';
import 'package:dostafka/app/v2/components/common/d_text.dart';

void main() {
  testWidgets('DText displays main text only when no label is provided', (WidgetTester tester) async {
    // Arrange
    const text = 'Hello, World!';

    // Act
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: DText(text),
        ),
      ),
    );

    // Assert
    expect(find.text(text), findsOneWidget);
  });

  testWidgets('DText displays label above main text when labelLocation is top', (WidgetTester tester) async {
    // Arrange
    const text = 'Main Text';
    const label = 'Label';

    // Act
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: DText(
            text,
            label: label,
            labelLocation: DLocation.top,
          ),
        ),
      ),
    );

    // Assert
    final labelFinder = find.text(label);
    final textFinder = find.text(text);
    expect(labelFinder, findsOneWidget);
    expect(textFinder, findsOneWidget);

    // Check that the label appears before the main text in a Column
    expect(
      tester.getTopLeft(labelFinder).dy < tester.getTopLeft(textFinder).dy,
      true,
    );
  });

  testWidgets('DText applies custom font size and overflow correctly', (WidgetTester tester) async {
    // Arrange
    const text = 'This is a very long text that might overflow.';
    final fontSize = DSize.large.getFontSize();

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: DText(
            text,
            size: DSize.large,
            overflow: TextOverflow.ellipsis,
            textStyle: TextStyle(fontSize: fontSize),
          ),
        ),
      ),
    );

    // Assert
    final textWidget = tester.widget<Text>(find.textContaining('This is a very long text'));
    expect(textWidget.style?.fontSize, fontSize);
    expect(textWidget.overflow, TextOverflow.ellipsis);
  });

  testWidgets('DText aligns main text and label horizontally when labelLocation is right', (WidgetTester tester) async {
    // Arrange
    const text = 'Main Text';
    const label = 'Label';

    // Act
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: DText(
            text,
            label: label,
            labelLocation: DLocation.right,
            mainAlignment: MainAxisAlignment.center,
            crossAlignment: CrossAxisAlignment.center,
          ),
        ),
      ),
    );

    // Assert
    final labelFinder = find.text(label);
    final textFinder = find.text(text);

    expect(labelFinder, findsOneWidget);
    expect(textFinder, findsOneWidget);

    // Check that the main text appears to the left of the label in a Row
    expect(
      tester.getTopLeft(textFinder).dx < tester.getTopLeft(labelFinder).dx,
      true,
    );
  });
}